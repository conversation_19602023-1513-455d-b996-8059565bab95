{"name": "advanced-minecraft-bot-system", "version": "2.0.0", "description": "🎮 نظام متقدم ومحسن لإنشاء وإدارة بوتات ماينكرافت Java و Bedrock عبر التلغرام", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "npm ci --omit=dev", "test": "node test/validate-system.js", "test:health": "curl -f http://localhost:${PORT:-3001}/health || exit 1", "test:status": "curl -f http://localhost:${PORT:-3001}/status || exit 1", "test:config": "node -e \"require('./config'); console.log('✅ تم تحميل الإعدادات بنجاح')\"", "health": "node health-check.js", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "cleanup": "node scripts/cleanup.js", "monitor": "node scripts/monitor.js", "setup": "node scripts/setup.js", "validate": "node test/validate-system.js", "migrate": "node scripts/migrate.js", "migrate:up": "node scripts/migrate.js up", "migrate:down": "node scripts/migrate.js down", "db:setup": "node scripts/db-setup.js", "db:seed": "node scripts/db-seed.js", "production": "NODE_ENV=production node index.js", "railway:build": "npm ci --omit=dev", "railway:start": "NODE_ENV=production node index.js", "lint": "echo '<PERSON><PERSON> not configured'", "postinstall": "echo '✅ تم تثبيت التبعيات بنجاح'", "prebuild": "echo '🔧 بدء عملية البناء...'", "rebuild": "npm rebuild", "check-files": "node scripts/check-deployment.js", "predeploy": "npm run check-files"}, "keywords": ["minecraft", "bot", "telegram", "java", "bedrock", "mineflayer", "bedrock-protocol", "aternos", "advanced", "management", "automation"], "author": "Advanced Minecraft Bot System Developer", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/advanced-minecraft-bot-system.git"}, "dependencies": {"bedrock-protocol": "^3.47.0", "dotenv": "^16.6.1", "mineflayer": "^4.20.0", "node-telegram-bot-api": "^0.66.0", "node-fetch": "^2.7.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "mysql2": "^3.14.2", "pg": "^8.11.3", "mariadb": "^3.2.2", "fs-extra": "^11.2.0", "moment": "^2.30.1", "chalk": "^4.1.2", "inquirer": "^8.2.6", "archiver": "^7.0.1", "unzipper": "^0.12.3"}, "optionalDependencies": {"mongodb": "^6.3.0", "redis": "^4.6.13", "tedious": "^18.6.1", "oracledb": "^6.6.0"}, "devDependencies": {"nodemon": "^3.0.2"}}