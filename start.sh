#!/bin/bash

# 🚀 سكريبت بدء تشغيل نظام بوت ماينكرافت المتقدم
# Advanced Minecraft Bot System Startup Script

set -e  # إيقاف التنفيذ عند حدوث خطأ

echo "🎮 بدء تشغيل نظام بوت ماينكرافت المتقدم..."
echo "📅 التاريخ: $(date)"
echo "🌍 البيئة: ${NODE_ENV:-production}"
echo "📦 إصدار Node.js: $(node --version)"
echo "📦 إصدار npm: $(npm --version)"

# التحقق من وجود الملفات المطلوبة
echo "🔍 التحقق من الملفات المطلوبة..."

if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود!"
    exit 1
fi

if [ ! -f "index.js" ]; then
    echo "❌ ملف index.js غير موجود!"
    exit 1
fi

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."
mkdir -p logs backups temp data uploads

# التحقق من متغيرات البيئة المطلوبة
echo "🔧 التحقق من متغيرات البيئة..."

if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo "⚠️  تحذير: TELEGRAM_BOT_TOKEN غير محدد"
fi

# بدء التطبيق
echo "🚀 بدء تشغيل التطبيق..."
exec node index.js
