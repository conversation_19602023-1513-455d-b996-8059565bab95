#!/usr/bin/env node

/**
 * 🎮 نظام بوت ماينكرافت المتقدم
 * الملف الرئيسي لتشغيل النظام
 * 
 * @version 2.0.0
 * <AUTHOR> Minecraft Bot System
 */

// تحميل متغيرات البيئة
require('dotenv').config();

const fs = require('fs-extra');
const path = require('path');
const { EventEmitter } = require('events');

// زيادة حد المستمعين لتجنب تحذيرات الذاكرة
EventEmitter.defaultMaxListeners = 20;

// تحميل الوحدات الأساسية
const { config, validate } = require('./config');
const TelegramBotManager = require('./telegram-bot');
const HealthCheckServer = require('./health-check-server');
const BackupManager = require('./backup-manager');
const Logger = require('./logger');

class MinecraftBotSystem {
    constructor() {
        this.telegramBot = null;
        this.healthServer = null;
        this.backupManager = null;
        this.logger = null;
        this.isShuttingDown = false;
        this.startTime = new Date();
        
        // إعدادات النظام
        this.systemInfo = {
            name: 'Advanced Minecraft Bot System',
            version: '2.0.0',
            author: 'Advanced Bot Developer',
            startTime: this.startTime,
            environment: process.env.NODE_ENV || 'production'
        };
    }

    // بدء تشغيل النظام
    async start() {
        try {
            console.log('🎮 نظام بوت ماينكرافت المتقدم');
            console.log('=====================================');
            console.log(`📦 الإصدار: ${this.systemInfo.version}`);
            console.log(`🌍 البيئة: ${this.systemInfo.environment}`);
            console.log(`⏰ وقت البدء: ${this.startTime.toLocaleString('ar-EG')}`);
            console.log('=====================================\n');

            // عرض شعار النظام
            this.displayLogo();

            // التحقق من متغيرات البيئة
            console.log('🔧 تم تحميل متغيرات البيئة');
            console.log('🚀 بدء تشغيل نظام بوت ماينكرافت...');

            // التحقق من صحة الإعدادات
            console.log('📋 التحقق من متغيرات البيئة...');
            const validation = validate();
            if (!validation.valid) {
                console.error('❌ أخطاء في الإعدادات:');
                validation.errors.forEach(error => console.error(`   ${error}`));
                if (config.development.debug) {
                    console.log('⚠️ تشغيل في وضع التطوير - متابعة رغم الأخطاء');
                } else {
                    process.exit(1);
                }
            }

            // إنشاء المجلدات المطلوبة
            await this.createRequiredDirectories();

            // تهيئة نظام السجلات
            await this.initializeLogger();

            // بدء خادم فحص الصحة
            await this.startHealthCheckServer();

            // تهيئة نظام النسخ الاحتياطي
            await this.initializeBackupManager();

            // تهيئة بوت التلغرام
            await this.initializeTelegramBot();

            // إعداد معالجات الإغلاق
            this.setupShutdownHandlers();

            // عرض معلومات النظام
            this.displaySystemInfo();

            console.log('\n✅ تم تشغيل النظام بنجاح!');
            console.log('📱 بوت التلغرام جاهز للاستخدام');
            console.log('🎮 يمكن الآن إنشاء بوتات ماينكرافت Java و Bedrock\n');

            this.displayFeatures();

            console.log('🔧 للحصول على المساعدة، استخدم الأمر /help في بوت التلغرام');

        } catch (error) {
            console.error('💥 فشل في تشغيل النظام:', error);
            await this.shutdown(1);
        }
    }

    // عرض شعار النظام
    displayLogo() {
        const logo = `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎮 Advanced Minecraft Bot System v2.0.0                  ║
║                                                              ║
║    ⚡ نظام متقدم لإدارة بوتات ماينكرافت                    ║
║    🤖 دعم Java و Bedrock Edition                            ║
║    📱 واجهة تلغرام سهلة الاستخدام                          ║
║    🔧 إدارة متقدمة ومراقبة ذكية                            ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        `;
        console.log(logo);
    }

    // إنشاء المجلدات المطلوبة
    async createRequiredDirectories() {
        const directories = [
            './logs',
            './backups',
            './temp',
            './data',
            './uploads'
        ];

        for (const dir of directories) {
            try {
                await fs.ensureDir(dir);
                console.log(`📁 تم إنشاء/التحقق من المجلد: ${dir}`);
            } catch (error) {
                console.error(`❌ خطأ في إنشاء المجلد ${dir}:`, error);
            }
        }
    }

    // تهيئة نظام السجلات
    async initializeLogger() {
        try {
            if (config.logging.file.enabled) {
                this.logger = new Logger();
                await this.logger.init();
                console.log('📝 تم تهيئة نظام السجلات');
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام السجلات:', error);
        }
    }

    // بدء خادم فحص الصحة
    async startHealthCheckServer() {
        try {
            this.healthServer = new HealthCheckServer(this);
            await this.healthServer.start();
            console.log('✅ تم بدء خادم فحص الصحة');
        } catch (error) {
            console.error('❌ خطأ في بدء خادم فحص الصحة:', error);
        }
    }

    // تهيئة نظام النسخ الاحتياطي
    async initializeBackupManager() {
        try {
            this.backupManager = new BackupManager();
            await this.backupManager.init();
            console.log('✅ تم تهيئة BackupManager بنجاح');

            // إنشاء مجلد النسخ الاحتياطي
            await fs.ensureDir('./backups/');
            console.log('📁 تم إنشاء مجلد النسخ الاحتياطي: ./backups/');

            // بدء النسخ الاحتياطي التلقائي إذا كان مفعلاً
            if (config.database.backup.autoBackup) {
                await this.backupManager.startAutoBackup();
                console.log('✅ تم تفعيل النسخ الاحتياطي التلقائي');
            }

        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام النسخ الاحتياطي:', error);
        }
    }

    // تهيئة بوت التلغرام
    async initializeTelegramBot() {
        try {
            // تحميل قائمة الأدمن
            const adminIds = process.env.ADMIN_IDS ? 
                process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) : 
                [];
            console.log('📋 تم تحميل قائمة الأدمن:', adminIds);

            // إنشاء وتهيئة بوت التلغرام
            this.telegramBot = new TelegramBotManager(this.backupManager);
            await this.telegramBot.init();

            console.log('🤖 تم تشغيل بوت التلغرام بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تهيئة بوت التلغرام:', error);
            throw error;
        }
    }

    // إعداد معالجات الإغلاق
    setupShutdownHandlers() {
        // معالجة إشارات الإغلاق
        const shutdownSignals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
        
        shutdownSignals.forEach(signal => {
            process.on(signal, async () => {
                console.log(`\n🛑 تم استلام إشارة الإغلاق: ${signal}`);
                await this.shutdown(0);
            });
        });

        // معالجة الأخطاء غير المتوقعة
        process.on('uncaughtException', async (error) => {
            console.error('💥 خطأ غير متوقع:', error);
            await this.shutdown(1);
        });

        process.on('unhandledRejection', async (reason, promise) => {
            console.error('💥 رفض غير معالج:', reason);
            console.error('في Promise:', promise);
            await this.shutdown(1);
        });

        console.log('🛡️ تم إعداد معالجات الإغلاق الآمن');
    }

    // عرض معلومات النظام
    displaySystemInfo() {
        const uptime = Date.now() - this.startTime.getTime();
        const memoryUsage = process.memoryUsage();

        console.log('\n📊 معلومات النظام:');
        console.log(`   🕐 وقت التشغيل: ${Math.floor(uptime / 1000)} ثانية`);
        console.log(`   💾 استخدام الذاكرة: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`);
        console.log(`   🔧 إصدار Node.js: ${process.version}`);
        console.log(`   🖥️ النظام: ${process.platform} ${process.arch}`);
        console.log(`   📁 مجلد العمل: ${process.cwd()}`);
    }

    // عرض الميزات المتاحة
    displayFeatures() {
        console.log('📋 الميزات المتاحة:');
        console.log('   • إنشاء بوتات ماينكرافت (Java & Bedrock)');
        console.log(`   • دعم آخر ${config.supportedVersions.java.length} إصدارات Java`);
        console.log(`   • دعم آخر ${config.supportedVersions.bedrock.length} إصدارات Bedrock`);
        console.log('   • التحكم الكامل في البوتات (تشغيل/إيقاف)');
        console.log('   • إرسال الرسائل والأوامر');
        console.log('   • مراقبة الإحصائيات والأداء');
        console.log('   • واجهة إدارة متقدمة للأدمن');
        console.log('   • نظام نسخ احتياطي تلقائي');
        console.log('   • مراقبة صحة النظام');
        console.log('   • دعم قواعد بيانات متعددة');
        console.log('   • نظام تنبيهات ذكي');
        console.log('   • إعادة اتصال تلقائي');
        console.log('   • حماية من البوتات المكررة');
        console.log('   • واجهة سهلة ومتطورة');
    }

    // الحصول على معلومات النظام
    getSystemStatus() {
        const uptime = Date.now() - this.startTime.getTime();
        const memoryUsage = process.memoryUsage();

        return {
            name: this.systemInfo.name,
            version: this.systemInfo.version,
            uptime: uptime,
            startTime: this.startTime,
            memory: {
                used: memoryUsage.heapUsed,
                total: memoryUsage.heapTotal,
                external: memoryUsage.external,
                rss: memoryUsage.rss
            },
            system: {
                platform: process.platform,
                arch: process.arch,
                nodeVersion: process.version,
                pid: process.pid
            },
            components: {
                telegramBot: this.telegramBot ? 'running' : 'stopped',
                healthServer: this.healthServer ? 'running' : 'stopped',
                backupManager: this.backupManager ? 'running' : 'stopped',
                logger: this.logger ? 'running' : 'stopped'
            }
        };
    }

    // إغلاق النظام بأمان
    async shutdown(exitCode = 0) {
        if (this.isShuttingDown) {
            console.log('⏳ الإغلاق قيد التنفيذ...');
            return;
        }

        this.isShuttingDown = true;
        console.log('\n🔄 بدء إغلاق النظام بأمان...');

        try {
            // إيقاف بوت التلغرام
            if (this.telegramBot) {
                console.log('📱 إغلاق بوت التلغرام...');
                await this.telegramBot.close();
            }

            // إيقاف نظام النسخ الاحتياطي
            if (this.backupManager) {
                console.log('💾 إغلاق نظام النسخ الاحتياطي...');
                await this.backupManager.close();
            }

            // إيقاف خادم فحص الصحة
            if (this.healthServer) {
                console.log('🏥 إغلاق خادم فحص الصحة...');
                await this.healthServer.close();
            }

            // إغلاق نظام السجلات
            if (this.logger) {
                console.log('📝 إغلاق نظام السجلات...');
                await this.logger.close();
            }

            const shutdownTime = Date.now() - this.startTime.getTime();
            console.log(`\n✅ تم إغلاق النظام بنجاح بعد ${Math.floor(shutdownTime / 1000)} ثانية من التشغيل`);
            console.log('👋 شكراً لاستخدام نظام بوت ماينكرافت المتقدم!');

        } catch (error) {
            console.error('❌ خطأ أثناء إغلاق النظام:', error);
        } finally {
            process.exit(exitCode);
        }
    }
}

// تشغيل النظام إذا تم استدعاؤه مباشرة
if (require.main === module) {
    const system = new MinecraftBotSystem();
    system.start().catch(error => {
        console.error('💥 فشل في بدء النظام:', error);
        process.exit(1);
    });
}

module.exports = MinecraftBotSystem;
