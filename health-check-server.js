/**
 * 🏥 خادم فحص الصحة المبسط
 * مراقبة حالة النظام وتوفير API للفحص الصحي
 */

const http = require('http');
const url = require('url');

class HealthCheckServer {
    constructor(mainSystem) {
        this.mainSystem = mainSystem;
        this.server = null;
        this.port = process.env.PORT || process.env.HEALTH_CHECK_PORT || 3001;
        this.host = process.env.HOST || process.env.HEALTH_CHECK_HOST || '0.0.0.0';
        this.startTime = new Date();
    }

    // بدء الخادم
    async start() {
        return new Promise((resolve, reject) => {
            try {
                console.log(`🏥 محاولة بدء خادم فحص الصحة على ${this.host}:${this.port}`);

                this.server = http.createServer((req, res) => {
                    this.handleRequest(req, res);
                });

                // إعداد timeout للخادم
                this.server.timeout = 30000;
                this.server.keepAliveTimeout = 5000;
                this.server.headersTimeout = 6000;

                this.server.listen(this.port, this.host, () => {
                    console.log(`🏥 خادم فحص الصحة يعمل على http://${this.host}:${this.port}`);
                    console.log(`🔗 Health Check URL: http://${this.host}:${this.port}/health`);
                    resolve();
                });

                this.server.on('error', (error) => {
                    console.error('❌ خطأ في خادم فحص الصحة:', error);
                    if (error.code === 'EADDRINUSE') {
                        console.error(`❌ المنفذ ${this.port} مستخدم بالفعل`);
                        console.log('💡 جاري المحاولة على منفذ آخر...');
                        // محاولة منفذ آخر
                        this.port = this.port + 1;
                        setTimeout(() => {
                            this.start().then(resolve).catch(reject);
                        }, 1000);
                        return;
                    }
                    reject(error);
                });

                this.server.on('listening', () => {
                    console.log(`✅ خادم فحص الصحة يستمع على المنفذ ${this.port}`);
                });

            } catch (error) {
                console.error('❌ خطأ في إنشاء خادم فحص الصحة:', error);
                reject(error);
            }
        });
    }

    // معالجة الطلبات
    async handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const pathname = parsedUrl.pathname;

        // إعداد CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        // معالجة OPTIONS request
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        try {
            if (pathname === '/health') {
                await this.handleHealthCheck(req, res);
            } else if (pathname === '/') {
                await this.handleRootEndpoint(req, res);
            } else if (pathname === '/status') {
                await this.handleStatusCheck(req, res);
            } else {
                this.handleNotFound(req, res);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة الطلب:', error);
            this.handleError(req, res, error);
        }
    }

    // فحص الصحة الأساسي
    async handleHealthCheck(req, res) {
        try {
            // فحص حالة النظام الأساسية
            const systemStatus = this.mainSystem ? this.mainSystem.getSystemStatus() : null;

            const healthStatus = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: Math.floor((Date.now() - this.startTime.getTime()) / 1000),
                version: '2.0.0',
                service: 'minecraft-bot-system',
                port: this.port,
                environment: process.env.NODE_ENV || 'production'
            };

            // إضافة معلومات النظام إذا كانت متاحة
            if (systemStatus) {
                healthStatus.system = {
                    memory: systemStatus.memory,
                    components: systemStatus.components
                };
            }

            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            });
            res.end(JSON.stringify(healthStatus, null, 2));

        } catch (error) {
            console.error('❌ خطأ في فحص الصحة:', error);

            res.writeHead(503, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: error.message,
                port: this.port,
                environment: process.env.NODE_ENV || 'production'
            }, null, 2));
        }
    }

    // معالجة الصفحة الرئيسية
    async handleRootEndpoint(req, res) {
        try {
            const welcomeMessage = {
                message: 'مرحباً بك في نظام بوت ماينكرافت المتقدم',
                service: 'minecraft-bot-system',
                version: '2.0.0',
                timestamp: new Date().toISOString(),
                endpoints: {
                    health: '/health',
                    status: '/status'
                }
            };

            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            });
            res.end(JSON.stringify(welcomeMessage, null, 2));

        } catch (error) {
            console.error('❌ خطأ في معالجة الصفحة الرئيسية:', error);
            this.handleError(req, res, error);
        }
    }

    // فحص حالة النظام المفصل
    async handleStatusCheck(req, res) {
        try {
            const systemStatus = this.mainSystem ? this.mainSystem.getSystemStatus() : null;

            const statusInfo = {
                service: 'minecraft-bot-system',
                version: '2.0.0',
                timestamp: new Date().toISOString(),
                uptime: Math.floor((Date.now() - this.startTime.getTime()) / 1000),
                port: this.port,
                environment: process.env.NODE_ENV || 'production',
                status: systemStatus ? 'running' : 'limited'
            };

            if (systemStatus) {
                statusInfo.system = systemStatus;
            }

            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            });
            res.end(JSON.stringify(statusInfo, null, 2));

        } catch (error) {
            console.error('❌ خطأ في فحص الحالة:', error);
            this.handleError(req, res, error);
        }
    }

    // معالجة الصفحات غير الموجودة
    handleNotFound(req, res) {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            error: 'Not Found',
            message: 'الصفحة المطلوبة غير موجودة',
            availableEndpoints: ['/', '/health', '/status']
        }, null, 2));
    }

    // معالجة الأخطاء
    handleError(req, res, error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            error: 'Internal Server Error',
            message: error.message
        }));
    }

    // إغلاق الخادم
    async close() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('✅ تم إغلاق خادم فحص الصحة');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = HealthCheckServer;
