/**
 * 📱 بوت التلغرام المتقدم
 * واجهة شاملة ومحسنة لإدارة بوتات ماينكرافت
 */

const TelegramBot = require('node-telegram-bot-api');
const { EventEmitter } = require('events');
const { spawn } = require('child_process');
const DatabaseManager = require('./database');
const BotManager = require('./bot-manager');
const { config } = require('./config');

class TelegramBotManager extends EventEmitter {
    constructor(backupManager = null) {
        super();
        this.bot = null;
        this.db = null;
        this.botManager = null;
        this.backupManager = backupManager;
        this.userSessions = new Map(); // userId -> session data
        this.rateLimiter = new Map(); // userId -> request count
        this.lastActionMessages = new Map(); // chatId -> messageId
        this.initialized = false;
        this.isRestarting = false; // علامة حالة إعادة التشغيل

        // إعدادات من الكونفيج
        this.telegramConfig = config.telegram;
        this.securityConfig = config.security;
        this.supportedVersions = config.supportedVersions;
    }

    // تهيئة بوت التلغرام
    async init() {
        if (this.initialized) return this;

        try {
            console.log('📱 تهيئة بوت التلغرام...');

            // تهيئة قاعدة البيانات
            this.db = new DatabaseManager();
            await this.db.init();

            // تهيئة مدير البوتات
            this.botManager = new BotManager();
            await this.botManager.init();

            // استعادة البوتات النشطة بعد إعادة التشغيل (بعد 5 ثوانٍ للسماح للنظام بالاستقرار)
            setTimeout(async () => {
                try {
                    console.log('🔄 فحص البوتات النشطة للاستعادة...');
                    await this.botManager.restoreActiveBots();
                } catch (error) {
                    console.error('❌ خطأ في استعادة البوتات النشطة:', error);
                }
            }, 5000);

            // إنشاء بوت التلغرام
            this.bot = new TelegramBot(this.telegramConfig.token, {
                polling: {
                    interval: this.telegramConfig.polling.interval,
                    autoStart: this.telegramConfig.polling.autoStart,
                    params: {
                        timeout: this.telegramConfig.polling.timeout
                    }
                }
            });

            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            this.setupBotManagerEvents();

            // بدء تنظيف الجلسات
            this.startSessionCleanup();

            // تعيين قائمة الأوامر
            await this.setCommands();

            this.initialized = true;
            console.log('✅ تم تهيئة بوت التلغرام بنجاح');
            
            return this;
        } catch (error) {
            console.error('❌ خطأ في تهيئة بوت التلغرام:', error);
            throw error;
        }
    }

    // إعداد معالجات أحداث التلغرام
    setupEventHandlers() {
        // معالج الرسائل النصية
        this.bot.on('message', async (msg) => {
            await this.handleMessage(msg);
        });

        // معالج الاستعلامات المضمنة
        this.bot.on('callback_query', async (query) => {
            await this.handleCallbackQuery(query);
        });

        // معالج الأخطاء
        this.bot.on('error', (error) => {
            console.error('❌ خطأ في بوت التلغرام:', error);
        });

        // معالج بدء التشغيل
        this.bot.on('polling_error', (error) => {
            console.error('❌ خطأ في polling:', error);
        });

        console.log('✅ تم إعداد معالجات أحداث التلغرام');
    }

    // إعداد معالجات أحداث مدير البوتات
    setupBotManagerEvents() {
        // عند اتصال البوت
        this.botManager.on('botConnected', async (data) => {
            await this.handleBotConnected(data);
        });

        // عند انقطاع البوت
        this.botManager.on('botDisconnected', async (data) => {
            await this.handleBotDisconnected(data);
        });

        // عند ركل البوت
        this.botManager.on('botKicked', async (data) => {
            await this.handleBotKicked(data);
        });

        // عند حدوث خطأ في البوت
        this.botManager.on('botError', async (data) => {
            await this.handleBotError(data);
        });

        // عند الوصول للحد الأقصى من محاولات إعادة الاتصال
        this.botManager.on('botMaxReconnectReached', async (data) => {
            await this.handleBotMaxReconnectReached(data);
        });

        // عند انقطاع السيرفر
        this.botManager.on('serverDown', async (data) => {
            await this.handleServerDown(data);
        });

        // عند الانقطاع النهائي للسيرفر
        this.botManager.on('serverDownFinal', async (data) => {
            await this.handleServerDownFinal(data);
        });

        // عند اكتشاف بوت مكرر
        this.botManager.on('botDuplicateName', async (data) => {
            await this.handleBotDuplicateName(data);
        });

        // عند دخول البوت للعالم بنجاح
        this.botManager.on('botWorldJoined', async (data) => {
            await this.handleBotWorldJoined(data);
        });

        // عند عدم توافق الإصدار
        this.botManager.on('botVersionMismatch', async (data) => {
            await this.handleBotVersionMismatch(data);
        });

        // معالجة إعادة تشغيل البوتات التلقائية بعد استعادة النسخة الاحتياطية
        this.botManager.on('botAutoRestarted', async (data) => {
            await this.handleBotAutoRestarted(data);
        });

        this.botManager.on('botAutoRestartFailed', async (data) => {
            await this.handleBotAutoRestartFailed(data);
        });

        // معالجة اكتمال استعادة البوتات
        this.botManager.on('botsRestoreCompleted', async (data) => {
            await this.handleBotsRestoreCompleted(data);
        });

        // معالجات أحداث النسخ الاحتياطي
        if (this.backupManager) {
            // عند اكتمال النسخ التلقائي
            this.backupManager.on('autoBackupCompleted', async (backup) => {
                await this.sendAutoBackupToAdmin(backup);
            });

            // عند خطأ في النسخ التلقائي
            this.backupManager.on('autoBackupError', async (error) => {
                await this.notifyAdminBackupError(error);
            });

            // ملاحظة: تم إزالة معالجات النظام القديم لإعادة التشغيل
            // النظام الآن يستخدم الاستعادة المباشرة بدون إعادة تشغيل

            // عند اكتمال الاستعادة المباشرة للنسخة الاحتياطية (بدون إعادة تشغيل)
            this.backupManager.on('backupRestoredLive', async (data) => {
                console.log('✅ تم استعادة النسخة الاحتياطية بنجاح بدون إعادة تشغيل');

                try {
                    // إرسال رسالة للمدير بنجاح الاستعادة
                    if (data.chatId) {
                        await this.bot.sendMessage(data.chatId,
                            '✅ **تم استعادة النسخة الاحتياطية بنجاح!**\n\n' +
                            '🔄 **تم تحديث قاعدة البيانات بدون إعادة تشغيل**\n' +
                            '🤖 **جميع البوتات تعمل بالبيانات الجديدة**\n' +
                            '⚡ **النظام جاهز للاستخدام فوراً**',
                            { parse_mode: 'Markdown' }
                        );

                        // عرض لوحة الأدمن مرة أخرى
                        setTimeout(async () => {
                            try {
                                await this.showAdminPanel(data.chatId);
                            } catch (error) {
                                console.error('❌ خطأ في عرض لوحة الأدمن بعد الاستعادة:', error);
                            }
                        }, 2000);
                    }

                } catch (error) {
                    console.error('❌ خطأ في إرسال رسالة نجاح الاستعادة:', error);
                }
            });

            // عند إعادة تهيئة قاعدة البيانات
            this.backupManager.on('databaseReinitialized', async (data) => {
                console.log('🔄 إعادة تهيئة جميع اتصالات قاعدة البيانات بعد الاستعادة...');

                try {
                    // انتظار قليل للتأكد من اكتمال استبدال الملف
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // إعادة تهيئة قاعدة البيانات في telegram-bot
                    if (this.db) {
                        console.log('🔄 إعادة تهيئة قاعدة البيانات في telegram-bot...');
                        // إعادة تعيين حالة التهيئة لإجبار إعادة التهيئة
                        this.db.initialized = false;
                        this.db.isConnected = false;
                        await this.db.init(true);
                        console.log('✅ تم إعادة تهيئة قاعدة البيانات في telegram-bot بنجاح');
                    }

                    // إعادة تهيئة botManager إذا كان متاحاً
                    if (this.botManager && this.botManager.db) {
                        console.log('🔄 إعادة تهيئة قاعدة البيانات في botManager...');
                        // إعادة تعيين حالة التهيئة لإجبار إعادة التهيئة
                        this.botManager.db.initialized = false;
                        this.botManager.db.isConnected = false;
                        await this.botManager.db.init(true);
                        console.log('✅ تم إعادة تهيئة قاعدة البيانات في botManager بنجاح');
                    }

                    // التحقق من حالة الاتصال وتحديثها
                    if (this.db && this.db.isConnected) {
                        console.log('✅ تم تأكيد حالة اتصال قاعدة البيانات');
                    } else if (this.db) {
                        console.log('⚠️ تحديث حالة اتصال قاعدة البيانات...');
                        this.db.isConnected = true;
                    }

                    console.log('✅ تم إعادة تهيئة جميع اتصالات قاعدة البيانات بنجاح');

                    // استعادة البوتات النشطة بعد إعادة تهيئة قاعدة البيانات
                    if (this.botManager) {
                        console.log('🔄 استعادة البوتات النشطة بعد استعادة النسخة الاحتياطية...');
                        setTimeout(async () => {
                            try {
                                await this.botManager.restoreActiveBots();
                                console.log('✅ تم استعادة البوتات النشطة بنجاح');
                            } catch (error) {
                                console.error('❌ خطأ في استعادة البوتات النشطة:', error);
                            }
                        }, 2000); // انتظار 2 ثانية للتأكد من استقرار النظام
                    }
                } catch (error) {
                    console.error('❌ خطأ في إعادة تهيئة اتصالات قاعدة البيانات:', error);
                }
            });
        }

        console.log('✅ تم إعداد معالجات أحداث مدير البوتات');

        // التحقق من وجود backupManager
        if (this.backupManager) {
            console.log('✅ تم ربط نظام النسخ الاحتياطي بنجاح');
        } else {
            console.log('⚠️ نظام النسخ الاحتياطي غير متاح');
        }
    }

    // معالجة الرسائل
    async handleMessage(msg) {
        try {
            const chatId = msg.chat.id;
            const userId = msg.from.id;
            const text = msg.text;

            // فحص حالة إعادة التشغيل
            if (this.isRestarting) {
                await this.bot.sendMessage(chatId, '🔄 النظام في حالة إعادة تشغيل، يرجى الانتظار...');
                return;
            }

            // فحص الحد الأقصى للطلبات
            if (!this.checkRateLimit(userId)) {
                await this.bot.sendMessage(chatId, '⚠️ تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً.');
                return;
            }

            // تسجيل المستخدم إذا لم يكن موجوداً
            await this.registerUserIfNotExists(userId, msg.from);

            // تحديث آخر نشاط للمستخدم
            await this.updateUserActivity(userId);

            // معالجة الأوامر
            if (text && text.startsWith('/')) {
                await this.handleCommand(msg);
            } else if (msg.document) {
                // معالجة الملفات المرسلة
                await this.handleDocumentMessage(msg);
            } else {
                // معالجة الرسائل النصية العادية
                await this.handleTextMessage(msg);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة الرسالة:', error);
            await this.bot.sendMessage(msg.chat.id, '❌ حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
        }
    }

    // معالجة الملفات المرسلة
    async handleDocumentMessage(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;
        const document = msg.document;

        // التحقق من صلاحيات الأدمن
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];

        if (!adminIds.includes(userId)) {
            return; // تجاهل الملفات من غير المدراء
        }

        // التحقق من وجود جلسة استعادة نسخة احتياطية
        const session = this.userSessions.get(chatId);
        if (!session || session.type !== 'admin_restore_backup' || session.step !== 'waiting_file') {
            return; // تجاهل الملف إذا لم تكن هناك جلسة استعادة
        }

        try {
            // التحقق من نوع الملف
            if (!document.file_name || !document.file_name.endsWith('.zip')) {
                await this.bot.sendMessage(chatId, '❌ يرجى إرسال ملف نسخة احتياطية بصيغة .zip فقط');
                return;
            }

            const statusMsg = await this.bot.sendMessage(chatId, '📥 جاري تحميل ملف النسخة الاحتياطية...');

            // تحميل الملف
            const fileInfo = await this.bot.getFile(document.file_id);
            const fileUrl = `https://api.telegram.org/file/bot${process.env.TELEGRAM_BOT_TOKEN}/${fileInfo.file_path}`;

            // تحميل الملف إلى مجلد مؤقت
            const fs = require('fs').promises;
            const path = require('path');
            const https = require('https');

            const tempDir = './temp';
            await fs.mkdir(tempDir, { recursive: true });
            const tempFilePath = path.join(tempDir, `restore_${Date.now()}.zip`);

            await new Promise((resolve, reject) => {
                const fsSync = require('fs');
                const file = fsSync.createWriteStream(tempFilePath);
                https.get(fileUrl, (response) => {
                    if (response.statusCode !== 200) {
                        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                        return;
                    }
                    response.pipe(file);
                    file.on('finish', () => {
                        file.close();
                        console.log(`📥 تم تحميل الملف: ${tempFilePath}`);
                        resolve();
                    });
                    file.on('error', reject);
                }).on('error', reject);
            });

            await this.bot.editMessageText('🔄 جاري استعادة النسخة الاحتياطية...', {
                chat_id: chatId,
                message_id: statusMsg.message_id
            });

            // استعادة النسخة الاحتياطية بدون إعادة تشغيل (محسن لـ Railway.com)
            if (this.backupManager) {
                try {
                    // استخدام النظام الجديد للاستعادة المباشرة (الآن افتراضي)
                    const result = await this.backupManager.restoreBackup(tempFilePath, this.db, chatId);

                    if (result.success) {
                        await this.bot.editMessageText('✅ تم استعادة النسخة الاحتياطية بنجاح!', {
                            chat_id: chatId,
                            message_id: statusMsg.message_id
                        });

                        const successMessage = `✅ **تم استعادة النسخة الاحتياطية بنجاح!**\n\n` +
                            `📁 **الملف:** ${document.file_name}\n` +
                            `📊 **الحجم:** ${Math.round(document.file_size / 1024)} KB\n` +
                            `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                            `⚡ **تم تحديث قاعدة البيانات فوراً بدون إعادة تشغيل**\n` +
                            `🤖 **جميع البوتات تعمل بالبيانات الجديدة**`;

                        const keyboard = {
                            inline_keyboard: [
                                [
                                    { text: '💾 إدارة النسخ', callback_data: 'admin_backup' },
                                    { text: '📊 عرض الإحصائيات', callback_data: 'admin_detailed_stats' }
                                ],
                                [
                                    { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                                ]
                            ]
                        };

                        const sentMessage = await this.bot.sendMessage(chatId, successMessage, {
                            parse_mode: 'Markdown',
                            reply_markup: keyboard
                        });

                        await this.setLastActionMessage(chatId, sentMessage.message_id);
                    } else {
                        await this.bot.editMessageText('❌ فشل في استعادة النسخة الاحتياطية', {
                            chat_id: chatId,
                            message_id: statusMsg.message_id
                        });
                    }
                } catch (error) {
                    console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
                    await this.bot.editMessageText('❌ حدث خطأ أثناء استعادة النسخة الاحتياطية', {
                        chat_id: chatId,
                        message_id: statusMsg.message_id
                    });
                }
            } else {
                await this.bot.editMessageText('❌ نظام النسخ الاحتياطي غير متاح', {
                    chat_id: chatId,
                    message_id: statusMsg.message_id
                });
            }

            // تنظيف الملف المؤقت
            try {
                await fs.unlink(tempFilePath);
            } catch (error) {
                // تجاهل خطأ عدم وجود الملف
            }

            // إنهاء الجلسة
            this.userSessions.delete(chatId);

        } catch (error) {
            console.error('❌ خطأ في معالجة ملف الاستعادة:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في معالجة ملف النسخة الاحتياطية');

            // إنهاء الجلسة
            this.userSessions.delete(chatId);
        }
    }

    // معالجة الأوامر
    async handleCommand(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;
        const command = msg.text.split(' ')[0].toLowerCase();

        // تنظيف المحادثة قبل إرسال الرد
        await this.cleanChat(chatId);

        switch (command) {
            case '/start':
                await this.handleStartCommand(msg);
                break;
            case '/help':
                await this.handleHelpCommand(msg);
                break;
            case '/menu':
                await this.handleMenuCommand(msg);
                break;
            case '/mybots':
                await this.handleMyBotsCommand(msg);
                break;
            case '/newbot':
                await this.handleNewBotCommand(msg);
                break;
            case '/stats':
                await this.handleStatsCommand(msg);
                break;
            case '/admin':
                await this.handleAdminCommand(msg);
                break;
            default:
                await this.handleUnknownCommand(msg);
        }
    }

    // معالجة أمر البداية
    async handleStartCommand(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;
        const firstName = msg.from.first_name || 'المستخدم';

        const welcomeMessage = `🎮 **مرحباً ${firstName}!**\n\n` +
            `أهلاً بك في **نظام بوت ماينكرافت المتقدم** 🚀\n\n` +
            `🤖 **الميزات المتاحة:**\n` +
            `• إنشاء بوتات ماينكرافت Java و Bedrock\n` +
            `• دعم أحدث الإصدارات\n` +
            `• إدارة متقدمة للبوتات\n` +
            `• مراقبة الأداء والإحصائيات\n` +
            `• نظام تنبيهات ذكي\n` +
            `• واجهة سهلة الاستخدام\n\n` +
            `📋 **الإصدارات المدعومة:**\n` +
            `☕ **Java:** ${this.supportedVersions.java.slice(0, 3).join(', ')} وأكثر\n` +
            `🪨 **Bedrock:** ${this.supportedVersions.bedrock.slice(0, 3).join(', ')} وأكثر\n\n` +
            `🚀 **ابدأ الآن بإنشاء بوتك الأول!**`;

        // التحقق من صلاحيات الأدمن
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];
        const isAdmin = adminIds.includes(userId);

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' },
                    { text: '📋 بوتاتي', callback_data: 'my_bots' }
                ],
                [
                    { text: '📊 الإحصائيات', callback_data: 'stats' },
                    { text: '❓ المساعدة', callback_data: 'help' }
                ],
                [
                    { text: '⚙️ الإعدادات', callback_data: 'settings' }
                ]
            ]
        };

        // إضافة زر الأدمن للمدراء فقط
        if (isAdmin) {
            keyboard.inline_keyboard.push([
                { text: '👑 لوحة الأدمن', callback_data: 'admin_panel' }
            ]);
        }

        const sentMessage = await this.bot.sendMessage(chatId, welcomeMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة أمر المساعدة
    async handleHelpCommand(msg) {
        const chatId = msg.chat.id;

        const helpMessage = `❓ **دليل الاستخدام**\n\n` +
            `🤖 **إدارة البوتات:**\n` +
            `• /newbot - إنشاء بوت جديد\n` +
            `• /mybots - عرض بوتاتك\n` +
            `• /stats - إحصائيات مفصلة\n\n` +
            `⚙️ **الأوامر العامة:**\n` +
            `• /start - البداية والقائمة الرئيسية\n` +
            `• /menu - القائمة الرئيسية\n` +
            `• /help - هذه المساعدة\n\n` +
            `🎮 **أنواع البوتات المدعومة:**\n` +
            `☕ **Java Edition:** للسيرفرات التقليدية\n` +
            `🪨 **Bedrock Edition:** للجوال والكونسول\n\n` +
            `🔧 **الميزات المتقدمة:**\n` +
            `• إعادة اتصال تلقائي\n` +
            `• مراقبة الحالة\n` +
            `• إرسال الرسائل والأوامر\n` +
            `• إحصائيات مفصلة\n` +
            `• تنبيهات ذكية\n\n` +
            `💡 **نصائح:**\n` +
            `• استخدم أسماء فريدة للبوتات\n` +
            `• تأكد من صحة عنوان السيرفر\n` +
            `• اختر الإصدار المناسب للسيرفر`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت', callback_data: 'new_bot' },
                    { text: '📋 بوتاتي', callback_data: 'my_bots' }
                ],
                [
                    { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, helpMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة أمر القائمة
    async handleMenuCommand(msg) {
        await this.showMainMenu(msg.chat.id, msg.from.id);
    }

    // معالجة أمر بوتاتي
    async handleMyBotsCommand(msg) {
        await this.showMyBots(msg.chat.id, msg.from.id);
    }

    // معالجة أمر إنشاء بوت جديد
    async handleNewBotCommand(msg) {
        await this.showNewBotMenu(msg.chat.id);
    }

    // معالجة أمر الإحصائيات
    async handleStatsCommand(msg) {
        await this.showStats(msg.chat.id, msg.from.id);
    }

    // معالجة أمر الأدمن
    async handleAdminCommand(msg) {
        const userId = msg.from.id;
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];

        if (!adminIds.includes(userId)) {
            await this.bot.sendMessage(msg.chat.id, '❌ ليس لديك صلاحية للوصول لوحة الأدمن');
            return;
        }

        await this.showAdminPanel(msg.chat.id);
    }

    // معالجة الأوامر غير المعروفة
    async handleUnknownCommand(msg) {
        const chatId = msg.chat.id;
        const command = msg.text.split(' ')[0];

        const unknownMessage = `❓ **أمر غير معروف**\n\n` +
            `الأمر \`${command}\` غير موجود.\n\n` +
            `📋 **الأوامر المتاحة:**\n` +
            `• /start - البداية\n` +
            `• /help - المساعدة\n` +
            `• /menu - القائمة الرئيسية\n` +
            `• /newbot - إنشاء بوت جديد\n` +
            `• /mybots - بوتاتي\n` +
            `• /stats - الإحصائيات`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' },
                    { text: '❓ المساعدة', callback_data: 'help' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, unknownMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة الرسائل النصية العادية
    async handleTextMessage(msg) {
        const chatId = msg.chat.id;
        const text = msg.text;

        // إذا كان المستخدم في جلسة إنشاء بوت
        const userSession = this.userSessions.get(msg.from.id);
        if (userSession && userSession.state === 'creating_bot') {
            await this.handleBotCreationInput(msg, userSession);
            return;
        }

        // إذا كان المستخدم في انتظار إدخال حد البوتات المخصص
        if (userSession && userSession.step === 'waiting_bot_limit') {
            await this.handleCustomBotLimitInput(msg, userSession);
            return;
        }

        // رسالة افتراضية للنصوص العادية
        const textMessage = `💬 **رسالة مستلمة**\n\n` +
            `شكراً لك على رسالتك! 😊\n\n` +
            `💡 **للتفاعل مع النظام:**\n` +
            `• استخدم الأزرار أدناه\n` +
            `• أو استخدم الأوامر مثل /help\n\n` +
            `🎮 **جاهز لإنشاء بوت ماينكرافت؟**`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' },
                    { text: '📋 بوتاتي', callback_data: 'my_bots' }
                ],
                [
                    { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, textMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // عرض لوحة الأدمن
    async showAdminPanel(chatId) {
        try {
            // فحص حالة إعادة التشغيل
            if (this.isRestarting) {
                await this.bot.sendMessage(chatId, '🔄 النظام في حالة إعادة تشغيل، يرجى الانتظار...');
                return;
            }

            // فحص حالة قاعدة البيانات
            if (!this.db || !this.db.isConnected) {
                await this.bot.sendMessage(chatId, '⚠️ قاعدة البيانات غير متاحة حالياً. يرجى المحاولة لاحقاً.');
                return;
            }

            const totalUsers = await this.db.getTotalUsers();
            const totalBots = await this.db.getTotalBots();
            const activeBots = this.botManager ? this.botManager.activeBots.size : 0;
            const systemUptime = Math.floor(process.uptime());
            const memoryUsage = Math.round(process.memoryUsage().heapUsed / 1024 / 1024);

            const adminMessage = `👑 **لوحة تحكم الأدمن**\n\n` +
                `📊 **إحصائيات النظام:**\n` +
                `• المستخدمين: ${totalUsers}\n` +
                `• إجمالي البوتات: ${totalBots}\n` +
                `• البوتات النشطة: ${activeBots}\n` +
                `• وقت التشغيل: ${systemUptime} ثانية\n` +
                `• استخدام الذاكرة: ${memoryUsage} MB\n\n` +
                `⚙️ **إدارة النظام:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '📊 إحصائيات مفصلة', callback_data: 'admin_detailed_stats' },
                        { text: '👥 إدارة المستخدمين', callback_data: 'admin_users' }
                    ],
                    [
                        { text: '🤖 إدارة البوتات', callback_data: 'admin_bots' },
                        { text: '💾 النسخ الاحتياطي', callback_data: 'admin_backup' }
                    ],
                    [
                        { text: '📝 السجلات', callback_data: 'admin_logs' },
                        { text: '⚙️ إعدادات النظام', callback_data: 'admin_settings' }
                    ],
                    [
                        { text: '🚨 إدارة التحذيرات', callback_data: 'admin_alerts' },
                        { text: '🔄 إعادة تشغيل النظام', callback_data: 'admin_restart' }
                    ],
                    [
                        { text: '📢 إرسال إعلان', callback_data: 'admin_broadcast' },
                        { text: '🗄️ إدارة قاعدة البيانات', callback_data: 'admin_database' }
                    ],
                    [
                        { text: '🛡️ حظر/إلغاء حظر', callback_data: 'admin_ban' },
                        { text: '📈 تقارير مفصلة', callback_data: 'admin_reports' }
                    ],
                    [
                        { text: '🔢 تحديد حد البوتات', callback_data: 'admin_bot_limit' }
                    ],
                    [
                        { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, adminMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض لوحة الأدمن:', error);

            // فحص إذا كان الخطأ بسبب قاعدة البيانات المغلقة
            if (error.message && error.message.includes('Database connection is closed')) {
                await this.bot.sendMessage(chatId, '⚠️ النظام في حالة إعادة تشغيل، يرجى الانتظار...');
            } else if (this.isRestarting) {
                await this.bot.sendMessage(chatId, '🔄 النظام في حالة إعادة تشغيل، يرجى الانتظار...');
            } else {
                await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض لوحة الأدمن');
            }
        }
    }

    // معالجة الاستعلامات المضمنة
    async handleCallbackQuery(query) {
        try {
            const chatId = query.message.chat.id;
            const userId = query.from.id;
            const data = query.data;

            console.log(`🔔 تم استلام callback query: ${data} من المستخدم: ${userId}`);

            // فحص حالة إعادة التشغيل
            if (this.isRestarting) {
                await this.bot.answerCallbackQuery(query.id, {
                    text: '🔄 النظام في حالة إعادة تشغيل، يرجى الانتظار...',
                    show_alert: true
                });
                return;
            }

            // فحص الحد الأقصى للطلبات
            if (!this.checkRateLimit(userId)) {
                await this.bot.answerCallbackQuery(query.id, {
                    text: '⚠️ تم تجاوز الحد الأقصى للطلبات',
                    show_alert: true
                });
                return;
            }

            // تحديث آخر نشاط للمستخدم
            await this.updateUserActivity(userId);

            // معالجة الاستعلام
            await this.processCallbackQuery(query);

            // الرد على الاستعلام
            try {
                await this.bot.answerCallbackQuery(query.id);
            } catch (error) {
                console.log(`تجاهل خطأ answerCallbackQuery: ${error.message}`);
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة callback query:', error);
            try {
                await this.bot.answerCallbackQuery(query.id, {
                    text: '❌ حدث خطأ في معالجة طلبك',
                    show_alert: true
                });
            } catch (answerError) {
                console.error('❌ خطأ في الرد على callback query:', answerError);
            }
        }
    }

    // معالجة الاستعلامات المضمنة
    async processCallbackQuery(query) {
        const chatId = query.message.chat.id;
        const userId = query.from.id;
        const data = query.data;

        // تنظيف المحادثة
        await this.cleanChat(chatId);

        switch (data) {
            case 'main_menu':
                await this.showMainMenu(chatId, userId);
                break;
            case 'new_bot':
                await this.showNewBotMenu(chatId, userId);
                break;
            case 'my_bots':
                await this.showMyBots(chatId, userId);
                break;
            case 'stats':
                await this.showStats(chatId, userId);
                break;
            case 'help':
                await this.showHelp(chatId);
                break;
            case 'settings':
                await this.showSettings(chatId, userId);
                break;
            case 'admin_panel':
                // التحقق من صلاحيات الأدمن
                const adminIds = process.env.ADMIN_IDS ?
                    process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
                    [];
                if (adminIds.includes(userId)) {
                    await this.showAdminPanel(chatId);
                } else {
                    await this.bot.sendMessage(chatId, '❌ ليس لديك صلاحية للوصول لوحة الأدمن');
                }
                break;
            default:
                // معالجة الاستعلامات المعقدة
                await this.handleComplexCallbackQuery(query);
        }
    }

    // فحص الحد الأقصى للطلبات
    checkRateLimit(userId) {
        const now = Date.now();
        const windowStart = now - this.securityConfig.protection.rateLimitWindow;
        
        if (!this.rateLimiter.has(userId)) {
            this.rateLimiter.set(userId, []);
        }

        const userRequests = this.rateLimiter.get(userId);
        
        // إزالة الطلبات القديمة
        const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
        
        // فحص الحد الأقصى
        if (validRequests.length >= this.securityConfig.protection.maxRequestsPerWindow) {
            return false;
        }

        // إضافة الطلب الحالي
        validRequests.push(now);
        this.rateLimiter.set(userId, validRequests);
        
        return true;
    }

    // تسجيل المستخدم إذا لم يكن موجوداً
    async registerUserIfNotExists(userId, userInfo) {
        try {
            // فحص حالة قاعدة البيانات
            if (!this.db || !this.db.isConnected) {
                console.log('⚠️ تجاهل تسجيل المستخدم - قاعدة البيانات غير متاحة');
                return;
            }

            const existingUser = await this.db.getUser(userId);
            if (!existingUser) {
                await this.db.createUser(userId, {
                    username: userInfo.username,
                    first_name: userInfo.first_name,
                    last_name: userInfo.last_name,
                    language_code: userInfo.language_code
                });
                console.log(`✅ تم تسجيل مستخدم جديد: ${userId}`);
            }
        } catch (error) {
            // فحص إذا كان الخطأ بسبب قاعدة البيانات المغلقة
            if (error.code === 'SQLITE_MISUSE' || error.message.includes('Database is closed')) {
                console.log('⚠️ تجاهل تسجيل المستخدم - قاعدة البيانات مغلقة');
                return;
            }
            console.error('❌ خطأ في تسجيل المستخدم:', error);
        }
    }

    // تحديث آخر نشاط للمستخدم
    async updateUserActivity(userId) {
        try {
            // فحص حالة قاعدة البيانات
            if (!this.db || !this.db.isConnected) {
                console.log('⚠️ تجاهل تحديث نشاط المستخدم - قاعدة البيانات غير متاحة');
                return;
            }

            await this.db.updateUser(userId, {
                last_activity: new Date().toISOString()
            });
        } catch (error) {
            // فحص إذا كان الخطأ بسبب قاعدة البيانات المغلقة
            if (error.code === 'SQLITE_MISUSE' || error.message.includes('Database is closed')) {
                console.log('⚠️ تجاهل تحديث نشاط المستخدم - قاعدة البيانات مغلقة');
                return;
            }
            console.error('❌ خطأ في تحديث نشاط المستخدم:', error);
        }
    }

    // تنظيف المحادثة
    async cleanChat(chatId, keepMessages = 2) {
        try {
            console.log(`🧹 تنظيف المحادثة ${chatId} (الحفاظ على ${keepMessages} رسالة فقط)`);

            // تهيئة مجموعة رسائل النسخ الاحتياطية إذا لم تكن موجودة
            if (!this.backupMessages) this.backupMessages = new Set();

            // الحصول على آخر رسالة إجراء
            const lastActionMessageId = this.lastActionMessages.get(chatId);

            if (lastActionMessageId) {
                // حذف الرسائل القديمة (محاولة حذف آخر 10 رسائل)
                let deletedCount = 0;
                for (let i = 1; i <= 10; i++) {
                    try {
                        const messageIdToDelete = lastActionMessageId - i;
                        if (messageIdToDelete > 0) {
                            // تحقق من أن الرسالة ليست رسالة نسخة احتياطية
                            if (!this.backupMessages.has(messageIdToDelete)) {
                                await this.bot.deleteMessage(chatId, messageIdToDelete);
                                deletedCount++;
                            } else {
                                console.log(`💾 تم تجاهل حذف رسالة النسخة الاحتياطية: ${messageIdToDelete}`);
                            }
                        }
                    } catch (error) {
                        // تجاهل أخطاء حذف الرسائل (قد تكون محذوفة بالفعل)
                    }
                }

                if (deletedCount > 0) {
                    console.log(`🗑️ تم حذف ${deletedCount} رسالة من المحادثة ${chatId}`);
                } else {
                    console.log(`✅ المحادثة نظيفة - ${keepMessages} رسالة فقط: الترحيب + آخر إجراء`);
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تنظيف المحادثة:', error);
        }
    }

    // حفظ آخر رسالة إجراء
    async setLastActionMessage(chatId, messageId) {
        this.lastActionMessages.set(chatId, messageId);
        console.log(`💾 تم حفظ آخر رسالة إجراء: ${messageId}`);
    }

    // تعيين قائمة الأوامر
    async setCommands() {
        // الأوامر العامة لجميع المستخدمين
        const generalCommands = [
            { command: 'start', description: '🏠 البداية والقائمة الرئيسية' },
            { command: 'menu', description: '📋 القائمة الرئيسية' },
            { command: 'newbot', description: '🤖 إنشاء بوت جديد' },
            { command: 'mybots', description: '📋 عرض بوتاتي' },
            { command: 'stats', description: '📊 الإحصائيات' },
            { command: 'help', description: '❓ المساعدة' }
        ];

        // أوامر المدراء
        const adminCommands = [
            ...generalCommands,
            { command: 'admin', description: '👑 لوحة تحكم الأدمن' }
        ];

        try {
            // تعيين الأوامر العامة لجميع المستخدمين
            await this.bot.setMyCommands(generalCommands);

            // تعيين أوامر خاصة للمدراء
            const adminIds = process.env.ADMIN_IDS ?
                process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
                [];

            for (const adminId of adminIds) {
                try {
                    await this.bot.setMyCommands(adminCommands, {
                        scope: { type: 'chat', chat_id: adminId }
                    });
                } catch (error) {
                    console.log(`تعذر تعيين أوامر الأدمن للمستخدم ${adminId}`);
                }
            }

            console.log('✅ تم تعيين قائمة الأوامر في شريط الكتابة');
        } catch (error) {
            console.error('❌ خطأ في تعيين الأوامر:', error);
        }
    }

    // بدء تنظيف الجلسات
    startSessionCleanup() {
        setInterval(() => {
            this.cleanupExpiredSessions();
        }, this.securityConfig.sessions.cleanupInterval);

        console.log('🧹 تم بدء تنظيف الجلسات التلقائي');
    }

    // تنظيف الجلسات المنتهية الصلاحية
    cleanupExpiredSessions() {
        const now = Date.now();
        const sessionDuration = this.securityConfig.sessions.duration;

        for (const [userId, session] of this.userSessions.entries()) {
            if (now - session.lastActivity > sessionDuration) {
                this.userSessions.delete(userId);
            }
        }

        // تنظيف rate limiter
        for (const [userId, requests] of this.rateLimiter.entries()) {
            const windowStart = now - this.securityConfig.protection.rateLimitWindow;
            const validRequests = requests.filter(timestamp => timestamp > windowStart);
            
            if (validRequests.length === 0) {
                this.rateLimiter.delete(userId);
            } else {
                this.rateLimiter.set(userId, validRequests);
            }
        }
    }

    // ==========================================
    // معالجات أحداث مدير البوتات
    // ==========================================

    // معالجة اتصال البوت
    async handleBotConnected(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // التحقق من أن هذا اتصال حقيقي
            if (!data.isRealConnection) {
                console.log(`⚠️ تجاهل إشعار اتصال غير حقيقي للبوت ${data.botName}`);
                return;
            }

            // تنظيف المحادثة فقط - لا نرسل إشعار اتصال
            await this.cleanChat(user.telegram_id);

            // فقط تسجيل الاتصال بدون إرسال إشعار
            console.log(`✅ البوت ${data.botName} متصل بنجاح - انتظار دخول العالم`);

        } catch (error) {
            console.error('❌ خطأ في معالجة اتصال البوت:', error);
        }
    }

    // معالجة انقطاع البوت
    async handleBotDisconnected(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // تجاهل الانقطاعات السريعة أو غير الحقيقية
            if (!data.wasRealConnection || (data.connectionDuration && data.connectionDuration < 5000)) {
                console.log(`⚠️ تجاهل إشعار انقطاع سريع للبوت ${data.botName} (مدة: ${data.connectionDuration}ms)`);
                return;
            }

            const disconnectMessage = `🔌 **انقطع الاتصال**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `⏰ **وقت الانقطاع:** ${new Date().toLocaleString('ar-EG')}\n` +
                `⏱️ **مدة الاتصال:** ${Math.round((data.connectionDuration || 0) / 1000)} ثانية\n\n` +
                `🔄 **جاري محاولة إعادة الاتصال...**`;

            await this.bot.sendMessage(user.telegram_id, disconnectMessage, {
                parse_mode: 'Markdown'
            });

            console.log(`📤 تم إرسال إشعار انقطاع الاتصال للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة انقطاع البوت:', error);
        }
    }

    // معالجة ركل البوت
    async handleBotKicked(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const kickMessage = `👢 **تم ركل البوت**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `❌ **السبب:** ${data.reason}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `🔄 **جاري محاولة إعادة الاتصال...**`;

            await this.bot.sendMessage(user.telegram_id, kickMessage, {
                parse_mode: 'Markdown'
            });

            console.log(`📤 تم إرسال إشعار ركل البوت للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة ركل البوت:', error);
        }
    }

    // معالجة خطأ البوت
    async handleBotError(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const errorMessage = `❌ **خطأ في البوت**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🚨 **الخطأ:** ${data.error}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `🔧 **يرجى التحقق من إعدادات البوت**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔧 إعدادات البوت', callback_data: `bot_action_${data.botId}_settings` },
                        { text: '🔄 إعادة تشغيل', callback_data: `bot_action_${data.botId}_restart` }
                    ],
                    [
                        { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                    ]
                ]
            };

            await this.bot.sendMessage(user.telegram_id, errorMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            console.log(`📤 تم إرسال إشعار خطأ البوت للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة خطأ البوت:', error);
        }
    }

    // معالجة الوصول للحد الأقصى من محاولات إعادة الاتصال
    async handleBotMaxReconnectReached(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const failureMessage = `🛑 **فشل في إعادة الاتصال**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.host}:${data.port}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `❌ **تم الوصول للحد الأقصى من محاولات إعادة الاتصال**\n` +
                `🔌 **السيرفر غير متاح أو هناك مشكلة في الاتصال**\n\n` +
                `💡 **يمكنك إعادة تشغيل البوت لاحقاً عندما يعود السيرفر للعمل**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 إعادة تشغيل الآن', callback_data: `bot_action_${data.botId}_restart` },
                        { text: '🔧 إعدادات البوت', callback_data: `bot_action_${data.botId}_settings` }
                    ],
                    [
                        { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                    ]
                ]
            };

            await this.bot.sendMessage(user.telegram_id, failureMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            console.log(`📤 تم إرسال إشعار فشل إعادة الاتصال للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة فشل إعادة الاتصال:', error);
        }
    }

    // معالجة انقطاع السيرفر
    async handleServerDown(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const alertMessage = `⚠️ **تحذير انقطاع السيرفر ${data.alertCount}/5**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.host}:${data.port}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `🔄 **جاري المحاولة مرة أخرى...**\n` +
                `⏳ **المتبقي:** ${data.timeRemaining} دقيقة\n\n` +
                `💡 **إذا استمر الانقطاع، سيتم إيقاف البوت تلقائياً**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '⏹️ إيقاف البوت الآن', callback_data: `bot_action_${data.botId}_stop` }
                    ],
                    [
                        { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                    ]
                ]
            };

            await this.bot.sendMessage(user.telegram_id, alertMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            console.log(`📤 تم إرسال تحذير انقطاع السيرفر ${data.alertCount}/5 للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة انقطاع السيرفر:', error);
        }
    }

    // معالجة الانقطاع النهائي للسيرفر
    async handleServerDownFinal(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // تنظيف المحادثة
            await this.cleanChat(user.telegram_id);

            const finalMessage = `🛑 **تم إيقاف البوت نهائياً**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.host}:${data.port}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `❌ **السبب:** انقطاع السيرفر لأكثر من 5 دقائق\n\n` +
                `💡 **يمكنك إعادة تشغيل البوت عندما يعود السيرفر للعمل**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 إعادة تشغيل البوت', callback_data: `bot_action_${data.botId}_start` }
                    ],
                    [
                        { text: '🔧 إعدادات البوت', callback_data: `bot_action_${data.botId}_settings` },
                        { text: '🗑️ حذف البوت', callback_data: `bot_action_${data.botId}_delete` }
                    ],
                    [
                        { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(user.telegram_id, finalMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(user.telegram_id, sentMessage.message_id);
            console.log(`📤 تم إرسال إشعار الإيقاف النهائي للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة الإيقاف النهائي:', error);
        }
    }

    // معالجة البوت المكرر
    async handleBotDuplicateName(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // تنظيف المحادثة
            await this.cleanChat(user.telegram_id);

            const duplicateMessage = `🚫 **اسم البوت مكرر**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.host}:${data.port}\n\n` +
                `❌ **المشكلة:** يوجد بوت آخر بنفس الاسم متصل بالسيرفر\n\n` +
                `🔧 **الحلول:**\n` +
                `1️⃣ احذف البوت الحالي\n` +
                `2️⃣ أنشئ بوت جديد باسم مختلف\n` +
                `3️⃣ تأكد من عدم وجود بوت آخر بنفس الاسم\n\n` +
                `💡 **نصيحة:** استخدم أسماء فريدة لكل بوت`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🗑️ حذف البوت', callback_data: `bot_action_${data.botId}_delete` }
                    ],
                    [
                        { text: '🚀 إنشاء بوت جديد', callback_data: 'new_bot' }
                    ],
                    [
                        { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(user.telegram_id, duplicateMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(user.telegram_id, sentMessage.message_id);
            console.log(`📤 تم إرسال إشعار البوت المكرر للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة البوت المكرر:', error);
        }
    }

    // معالجة دخول البوت للعالم بنجاح
    async handleBotWorldJoined(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // تنظيف المحادثة
            await this.cleanChat(user.telegram_id);

            const editionIcon = data.edition === 'java' ? '☕' : '🪨';
            const successMessage = `🎉 **تم دخول العالم بنجاح!**\n\n` +
                `${editionIcon} **معلومات البوت:**\n` +
                `🤖 **الاسم:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.serverHost}:${data.serverPort}\n` +
                `📦 **النوع:** ${data.edition === 'java' ? 'Java Edition' : 'Bedrock Edition'}\n\n` +
                `✅ **الحالة:** متصل ونشط في العالم\n` +
                `🌍 **البوت جاهز لتلقي الأوامر والرسائل**\n\n` +
                `💡 **يمكنك الآن إرسال الرسائل والأوامر للبوت**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '💬 إرسال رسالة', callback_data: `bot_action_${data.botId}_message` },
                        { text: '⚡ تنفيذ أمر', callback_data: `bot_action_${data.botId}_command` }
                    ],
                    [
                        { text: '📊 إحصائيات البوت', callback_data: `bot_action_${data.botId}_stats` }
                    ],
                    [
                        { text: '🔙 إدارة البوت', callback_data: `bot_manage_${data.botId}` },
                        { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(user.telegram_id, successMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(user.telegram_id, sentMessage.message_id);
            console.log(`🎉 تم إرسال إشعار دخول العالم بنجاح للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة دخول العالم:', error);
        }
    }

    // معالجة عدم توافق الإصدار
    async handleBotVersionMismatch(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            // تنظيف المحادثة
            await this.cleanChat(user.telegram_id);

            // استخراج الإصدارات المدعومة من رسالة الخطأ
            const supportedVersionsMatch = data.serverMessage.match(/versions: ([0-9., -]+)/);
            const supportedVersions = supportedVersionsMatch ? supportedVersionsMatch[1] : 'غير محدد';

            const versionMismatchMessage = `⚠️ **مشكلة في الإصدار!**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `📦 **إصدار البوت:** ${data.botVersion}\n` +
                `🌐 **رسالة السيرفر:** عدم توافق الإصدار\n\n` +
                `✅ **الإصدارات المدعومة بواسطة السيرفر:**\n` +
                `${supportedVersions}\n\n` +
                `💡 **الحلول:**\n` +
                `1️⃣ أنشئ بوت جديد بإصدار مدعوم\n` +
                `2️⃣ تحقق من إعدادات السيرفر\n` +
                `3️⃣ تواصل مع مدير السيرفر\n\n` +
                `🔄 **البوت سيستمر في المحاولة حتى يتم حل المشكلة**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' }
                    ],
                    [
                        { text: '🔙 إدارة البوت', callback_data: `bot_manage_${data.botId}` },
                        { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(user.telegram_id, versionMismatchMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(user.telegram_id, sentMessage.message_id);
            console.log(`⚠️ تم إرسال تحذير عدم توافق الإصدار للمستخدم ${data.userId}`);

        } catch (error) {
            console.error('❌ خطأ في معالجة عدم توافق الإصدار:', error);
        }
    }

    // معالجة أوامر الأدمن الجديدة
    async handleAdminCallbacks(query) {
        const chatId = query.message.chat.id;
        const userId = query.from.id;
        const data = query.data;

        // التحقق من صلاحيات الأدمن
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];

        if (!adminIds.includes(userId)) {
            await this.bot.sendMessage(chatId, '❌ ليس لديك صلاحية للوصول لهذه الميزة');
            return;
        }

        switch (data) {
            case 'admin_detailed_stats':
                await this.showDetailedStats(chatId);
                break;
            case 'admin_users':
                await this.showUserManagement(chatId);
                break;
            case 'admin_bots':
                await this.showBotManagement(chatId);
                break;
            case 'admin_backup':
                await this.showBackupManagement(chatId);
                break;
            case 'admin_logs':
                await this.showSystemLogs(chatId);
                break;
            case 'admin_settings':
                await this.showSystemSettings(chatId);
                break;
            case 'admin_alerts':
                await this.showAlertManagement(chatId);
                break;
            case 'admin_restart':
                await this.showRestartConfirmation(chatId);
                break;
            case 'admin_broadcast':
                await this.showBroadcastMenu(chatId);
                break;
            case 'admin_database':
                await this.showDatabaseManagement(chatId);
                break;
            case 'admin_ban':
                await this.showBanManagement(chatId);
                break;
            case 'admin_reports':
                await this.showDetailedReports(chatId);
                break;
            case 'admin_panel':
                await this.showAdminPanel(chatId);
                break;
            case 'admin_create_backup':
                await this.createBackupNow(chatId);
                break;
            case 'admin_list_backups':
                await this.showBackupList(chatId);
                break;
            case 'admin_restore_backup':
                await this.showRestoreBackup(chatId);
                break;
            case 'admin_start_auto_backup':
                await this.showAutoBackupSetup(chatId);
                break;
            case 'admin_stop_auto_backup':
                await this.stopAutoBackup(chatId);
                break;
            case 'admin_send_latest_backup':
                await this.sendLatestBackup(chatId);
                break;
            case 'admin_cleanup_backups':
                await this.cleanupOldBackups(chatId);
                break;
            case 'admin_set_auto_backup_5':
                await this.setAutoBackup(chatId, 5);
                break;
            case 'admin_set_auto_backup_10':
                await this.setAutoBackup(chatId, 10);
                break;
            case 'admin_set_auto_backup_30':
                await this.setAutoBackup(chatId, 30);
                break;
            case 'admin_set_auto_backup_60':
                await this.setAutoBackup(chatId, 60);
                break;
            case 'admin_set_auto_backup_120':
                await this.setAutoBackup(chatId, 120);
                break;
            case 'admin_set_auto_backup_180':
                await this.setAutoBackup(chatId, 180);
                break;
            case 'admin_confirm_delete_all_backups':
                await this.confirmDeleteAllBackups(chatId);
                break;
            case 'admin_confirm_restart':
                await this.restartSystem(chatId);
                break;
            case 'admin_bot_limit':
                await this.showBotLimitSettings(chatId);
                break;
            default:
                await this.showAdminPanel(chatId);
        }
    }

    // ==========================================
    // دوال أوامر الأدمن الجديدة
    // ==========================================

    // عرض إحصائيات مفصلة
    async showDetailedStats(chatId) {
        try {
            const totalUsers = await this.db.getTotalUsers();
            const totalBots = await this.db.getTotalBots();
            const activeBots = this.botManager.activeBots.size;
            const systemUptime = Math.floor(process.uptime());
            const memoryUsage = Math.round(process.memoryUsage().heapUsed / 1024 / 1024);

            // إحصائيات إضافية
            const allBots = await this.db.getAllBots();
            const javaBots = allBots.filter(bot => bot.edition === 'java').length;
            const bedrockBots = allBots.filter(bot => bot.edition === 'bedrock').length;
            const runningBots = allBots.filter(bot => bot.status === 'running').length;
            const stoppedBots = allBots.filter(bot => bot.status === 'stopped').length;

            const statsMessage = `📊 **إحصائيات النظام المفصلة**\n\n` +
                `👥 **المستخدمين:**\n` +
                `• إجمالي المستخدمين: ${totalUsers}\n` +
                `• المستخدمين النشطين: ${this.rateLimiter.size}\n\n` +
                `🤖 **البوتات:**\n` +
                `• إجمالي البوتات: ${totalBots}\n` +
                `• البوتات النشطة: ${activeBots}\n` +
                `• بوتات Java: ${javaBots}\n` +
                `• بوتات Bedrock: ${bedrockBots}\n` +
                `• البوتات العاملة: ${runningBots}\n` +
                `• البوتات المتوقفة: ${stoppedBots}\n\n` +
                `💻 **النظام:**\n` +
                `• وقت التشغيل: ${Math.floor(systemUptime / 3600)}س ${Math.floor((systemUptime % 3600) / 60)}د\n` +
                `• استخدام الذاكرة: ${memoryUsage} MB\n` +
                `• إصدار Node.js: ${process.version}\n` +
                `• النظام: ${process.platform} ${process.arch}`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 تحديث', callback_data: 'admin_detailed_stats' },
                        { text: '📈 تقارير مفصلة', callback_data: 'admin_reports' }
                    ],
                    [
                        { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, statsMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض الإحصائيات المفصلة:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض الإحصائيات');
        }
    }

    // إدارة النسخ الاحتياطي
    async showBackupManagement(chatId) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح\n\nيرجى إعادة تشغيل النظام');
                return;
            }

            const backupHistory = await this.backupManager.getBackupHistory();
            const lastBackup = backupHistory.length > 0 ? backupHistory[0] : null;
            const totalSize = backupHistory.reduce((total, backup) => total + (backup.compressedSize || backup.size || 0), 0);
            const isAutoBackupEnabled = this.backupManager.isAutoBackupEnabled();

            const backupMessage = `💾 **إدارة النسخ الاحتياطي المتقدم**\n\n` +
                `📊 **الإحصائيات:**\n` +
                `• عدد النسخ: ${backupHistory.length}/5 (الحد الأقصى)\n` +
                `• الحجم الإجمالي: ${Math.round(totalSize / 1024)} KB\n` +
                `• آخر نسخة: ${lastBackup ? new Date(lastBackup.timestamp).toLocaleString('ar-EG') : 'لا توجد'}\n` +
                `• النسخ التلقائي: ${isAutoBackupEnabled ? '🟢 مفعل' : '🔴 معطل'}\n\n` +
                `⚙️ **العمليات المتاحة:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '💾 إنشاء نسخة فورية', callback_data: 'admin_create_backup' },
                        { text: '📋 عرض النسخ', callback_data: 'admin_list_backups' }
                    ],
                    [
                        { text: '📤 استعادة نسخة', callback_data: 'admin_restore_backup' },
                        { text: '🗑️ حذف النسخ القديمة', callback_data: 'admin_cleanup_backups' }
                    ],
                    [
                        { text: isAutoBackupEnabled ? '⏹️ إيقاف النسخ التلقائي' : '▶️ تشغيل النسخ التلقائي',
                          callback_data: isAutoBackupEnabled ? 'admin_stop_auto_backup' : 'admin_start_auto_backup' }
                    ],
                    [
                        { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, backupMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض إدارة النسخ الاحتياطي:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض إدارة النسخ الاحتياطي');
        }
    }

    // إرسال إعلان لجميع المستخدمين
    async showBroadcastMenu(chatId) {
        try {
            const broadcastMessage = `📢 **إرسال إعلان**\n\n` +
                `📝 **إرسل رسالتك التي تريد إرسالها لجميع المستخدمين:**\n\n` +
                `⚠️ **تحذير:** سيتم إرسال الرسالة لجميع المستخدمين المسجلين في النظام`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, broadcastMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

            // تفعيل وضع انتظار الرسالة
            this.userSessions.set(chatId, {
                type: 'admin_broadcast',
                step: 'waiting_message',
                startTime: Date.now()
            });

        } catch (error) {
            console.error('❌ خطأ في عرض قائمة الإعلان:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض قائمة الإعلان');
        }
    }

    // إعادة تشغيل النظام
    async showRestartConfirmation(chatId) {
        try {
            const restartMessage = `🔄 **إعادة تشغيل النظام**\n\n` +
                `⚠️ **تحذير:** سيتم إعادة تشغيل النظام بالكامل\n\n` +
                `📋 **ما سيحدث:**\n` +
                `• إيقاف جميع البوتات النشطة\n` +
                `• حفظ البيانات\n` +
                `• إعادة تشغيل النظام\n` +
                `• إعادة تشغيل البوتات تلقائياً\n\n` +
                `❓ **هل أنت متأكد من المتابعة؟**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '✅ نعم، أعد التشغيل', callback_data: 'admin_confirm_restart' },
                        { text: '❌ إلغاء', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, restartMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض تأكيد إعادة التشغيل:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض تأكيد إعادة التشغيل');
        }
    }

    // إدارة قاعدة البيانات
    async showDatabaseManagement(chatId) {
        try {
            const dbStats = await this.db.getDatabaseStats();

            const dbMessage = `🗄️ **إدارة قاعدة البيانات**\n\n` +
                `📊 **الإحصائيات:**\n` +
                `• نوع قاعدة البيانات: ${dbStats.type || 'SQLite'}\n` +
                `• حجم قاعدة البيانات: ${dbStats.size || 'غير محدد'}\n` +
                `• عدد الجداول: ${dbStats.tables || 'غير محدد'}\n\n` +
                `⚙️ **العمليات المتاحة:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🧹 تنظيف البيانات', callback_data: 'admin_cleanup_db' },
                        { text: '📊 إحصائيات مفصلة', callback_data: 'admin_db_stats' }
                    ],
                    [
                        { text: '🔧 صيانة قاعدة البيانات', callback_data: 'admin_db_maintenance' },
                        { text: '📤 تصدير البيانات', callback_data: 'admin_export_db' }
                    ],
                    [
                        { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, dbMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض إدارة قاعدة البيانات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض إدارة قاعدة البيانات');
        }
    }

    // إنشاء نسخة احتياطية فورية
    async createBackupNow(chatId) {
        try {
            const statusMsg = await this.bot.sendMessage(chatId, '💾 جاري إنشاء نسخة احتياطية مضغوطة...');

            if (this.backupManager) {
                const backup = await this.backupManager.createBackup('manual', `نسخة يدوية - ${new Date().toLocaleString('ar-EG')}`);

                if (backup) {
                    // إرسال ملف النسخة الاحتياطية (بدون حذف)
                    const backupMessage = await this.bot.sendDocument(chatId, backup.filepath, {
                        caption: `✅ تم إنشاء النسخة الاحتياطية بنجاح!\n\n` +
                            `📁 اسم الملف: ${backup.filename}\n` +
                            `📊 الحجم: ${Math.round(backup.compressedSize / 1024)} KB\n` +
                            `⏰ الوقت: ${new Date(backup.timestamp).toLocaleString('ar-EG')}\n\n` +
                            `💾 هذا الملف محفوظ ولن يتم حذفه من المحادثة`
                    });

                    // حذف رسالة الحالة فقط
                    await this.bot.deleteMessage(chatId, statusMsg.message_id);

                    const keyboard = {
                        inline_keyboard: [
                            [
                                { text: '💾 إدارة النسخ الاحتياطي', callback_data: 'admin_backup' },
                                { text: '🔙 لوحة الأدمن', callback_data: 'admin_panel' }
                            ]
                        ]
                    };

                    const sentMessage = await this.bot.sendMessage(chatId, '✅ تم إرسال النسخة الاحتياطية بنجاح!', {
                        reply_markup: keyboard
                    });

                    await this.setLastActionMessage(chatId, sentMessage.message_id);

                    // حفظ معرف رسالة النسخة الاحتياطية لمنع حذفها
                    if (!this.backupMessages) this.backupMessages = new Set();
                    this.backupMessages.add(backupMessage.message_id);
                } else {
                    await this.bot.editMessageText('❌ فشل إنشاء النسخة الاحتياطية', {
                        chat_id: chatId,
                        message_id: statusMsg.message_id
                    });
                }
            } else {
                await this.bot.editMessageText('❌ نظام النسخ الاحتياطي غير متاح', {
                    chat_id: chatId,
                    message_id: statusMsg.message_id
                });
            }

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في إنشاء النسخة الاحتياطية');
        }
    }

    // عرض قائمة النسخ الاحتياطية
    async showBackupList(chatId) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح');
                return;
            }

            const backupHistory = await this.backupManager.getBackupHistory();

            if (backupHistory.length === 0) {
                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '💾 إنشاء نسخة الآن', callback_data: 'admin_create_backup' },
                            { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, '📂 لا توجد نسخ احتياطية متاحة', {
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
                return;
            }

            let message = '📋 **قائمة النسخ الاحتياطية:**\n\n';

            // عرض آخر 5 نسخ فقط
            const recentBackups = backupHistory.slice(0, 5);

            for (let i = 0; i < recentBackups.length; i++) {
                const backup = recentBackups[i];
                const date = new Date(backup.timestamp);
                const dateStr = date.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
                const timeStr = date.toLocaleTimeString('ar-EG', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                const size = Math.round(backup.compressedSize / 1024) || Math.round(backup.size / 1024);

                message += `${i+1}. 📅 ${dateStr} ⏰ ${timeStr}\n`;
                message += `   📁 ${backup.filename || `${backup.id}.zip`}\n`;
                message += `   📊 ${size} KB • ${backup.type === 'auto' ? '🤖 تلقائي' : '👤 يدوي'}\n`;
                message += `   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
            }

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '📤 إرسال آخر نسخة', callback_data: 'admin_send_latest_backup' },
                        { text: '🗑️ حذف النسخ القديمة', callback_data: 'admin_cleanup_backups' }
                    ],
                    [
                        { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض قائمة النسخ الاحتياطية:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض قائمة النسخ الاحتياطية');
        }
    }

    // عرض واجهة استعادة النسخة الاحتياطية
    async showRestoreBackup(chatId) {
        try {
            const message = `📤 **استعادة نسخة احتياطية**\n\n` +
                `لاستعادة نسخة احتياطية، يرجى اتباع الخطوات التالية:\n\n` +
                `1️⃣ أرسل ملف النسخة الاحتياطية (بصيغة .zip)\n` +
                `2️⃣ انتظر حتى تكتمل عملية الاستعادة\n\n` +
                `⚡ **مميز:** الاستعادة تتم فوراً بدون إعادة تشغيل النظام\n` +
                `🔄 **سريع:** تحديث قاعدة البيانات مباشرة\n` +
                `🤖 **مستمر:** البوتات تعمل بالبيانات الجديدة فوراً\n\n` +
                `⚠️ **تحذير:** سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

            // تفعيل وضع انتظار ملف النسخة الاحتياطية
            this.userSessions.set(chatId, {
                type: 'admin_restore_backup',
                step: 'waiting_file',
                startTime: Date.now()
            });

        } catch (error) {
            console.error('❌ خطأ في عرض واجهة استعادة النسخة الاحتياطية:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض واجهة استعادة النسخة الاحتياطية');
        }
    }

    // عرض واجهة إعداد النسخ التلقائي
    async showAutoBackupSetup(chatId) {
        try {
            const message = `⏱️ **إعداد النسخ الاحتياطي التلقائي**\n\n` +
                `أدخل عدد الدقائق بين كل نسخة احتياطية تلقائية:\n\n` +
                `🕒 **الخيارات المقترحة:**\n` +
                `• 5 دقائق\n` +
                `• 10 دقائق\n` +
                `• 30 دقائق\n` +
                `• 60 دقائق (ساعة)\n\n` +
                `⚠️ **ملاحظة:** سيتم الاحتفاظ بآخر 5 نسخ فقط`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '5 دقائق', callback_data: 'admin_set_auto_backup_5' },
                        { text: '10 دقائق', callback_data: 'admin_set_auto_backup_10' }
                    ],
                    [
                        { text: '30 دقيقة', callback_data: 'admin_set_auto_backup_30' },
                        { text: '60 دقيقة', callback_data: 'admin_set_auto_backup_60' }
                    ],
                    [
                        { text: '2 ساعة', callback_data: 'admin_set_auto_backup_120' },
                        { text: '3 ساعات', callback_data: 'admin_set_auto_backup_180' }
                    ],
                    [
                        { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض واجهة إعداد النسخ التلقائي:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض واجهة إعداد النسخ التلقائي');
        }
    }

    // إيقاف النسخ التلقائي
    async stopAutoBackup(chatId) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح');
                return;
            }

            const result = this.backupManager.stopAutoBackup();

            if (result) {
                const message = `⏹️ **تم إيقاف النسخ الاحتياطي التلقائي**\n\n` +
                    `✅ تم إيقاف النسخ الاحتياطي التلقائي بنجاح`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '▶️ تشغيل النسخ التلقائي', callback_data: 'admin_start_auto_backup' },
                            { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
            } else {
                await this.bot.sendMessage(chatId, '❌ فشل إيقاف النسخ الاحتياطي التلقائي');
            }

        } catch (error) {
            console.error('❌ خطأ في إيقاف النسخ التلقائي:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في إيقاف النسخ التلقائي');
        }
    }

    // تشغيل النسخ التلقائي بمدة محددة
    async setAutoBackup(chatId, minutes) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح');
                return;
            }

            const result = this.backupManager.startAutoBackup(minutes);

            if (result) {
                const timeText = minutes >= 60 ?
                    `${Math.floor(minutes / 60)} ساعة${minutes % 60 > 0 ? ` و ${minutes % 60} دقيقة` : ''}` :
                    `${minutes} دقيقة`;

                const message = `▶️ **تم تشغيل النسخ الاحتياطي التلقائي**\n\n` +
                    `⏰ **المدة:** كل ${timeText}\n` +
                    `📦 **الاحتفاظ:** آخر 5 نسخ فقط\n` +
                    `📤 **الإرسال:** سيتم إرسال النسخ تلقائياً للمدير\n` +
                    `🔄 **التشغيل:** مستمر حتى الإيقاف اليدوي\n\n` +
                    `✅ النسخ التلقائي نشط الآن ويعمل في الخلفية`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '⏹️ إيقاف النسخ التلقائي', callback_data: 'admin_stop_auto_backup' },
                            { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
            } else {
                await this.bot.sendMessage(chatId, '❌ فشل تشغيل النسخ الاحتياطي التلقائي');
            }

        } catch (error) {
            console.error('❌ خطأ في تشغيل النسخ التلقائي:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في تشغيل النسخ التلقائي');
        }
    }

    // إرسال آخر نسخة احتياطية
    async sendLatestBackup(chatId) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح');
                return;
            }

            const backupHistory = await this.backupManager.getBackupHistory();

            if (backupHistory.length === 0) {
                await this.bot.sendMessage(chatId, '❌ لا توجد نسخ احتياطية متاحة');
                return;
            }

            const latestBackup = backupHistory[0];
            const backupPath = require('path').join('./backups/', latestBackup.filename);

            if (!require('fs').existsSync(backupPath)) {
                await this.bot.sendMessage(chatId, '❌ ملف النسخة الاحتياطية غير موجود');
                return;
            }

            const statusMsg = await this.bot.sendMessage(chatId, '📤 جاري إرسال آخر نسخة احتياطية...');

            const backupMessage = await this.bot.sendDocument(chatId, backupPath, {
                caption: `📦 **آخر نسخة احتياطية**\n\n` +
                    `📁 الملف: ${latestBackup.filename}\n` +
                    `📊 الحجم: ${Math.round(latestBackup.compressedSize / 1024)} KB\n` +
                    `⏰ التاريخ: ${new Date(latestBackup.timestamp).toLocaleString('ar-EG')}\n` +
                    `🏷️ النوع: ${latestBackup.type === 'auto' ? 'تلقائي' : 'يدوي'}\n\n` +
                    `💾 هذا الملف محفوظ ولن يتم حذفه من المحادثة`,
                parse_mode: 'Markdown'
            });

            // حفظ معرف رسالة النسخة الاحتياطية لمنع حذفها
            if (!this.backupMessages) this.backupMessages = new Set();
            this.backupMessages.add(backupMessage.message_id);

            await this.bot.deleteMessage(chatId, statusMsg.message_id);

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '📋 عرض جميع النسخ', callback_data: 'admin_list_backups' },
                        { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, '✅ تم إرسال آخر نسخة احتياطية بنجاح!', {
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في إرسال آخر نسخة احتياطية:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في إرسال النسخة الاحتياطية');
        }
    }

    // حذف جميع النسخ الاحتياطية
    async cleanupOldBackups(chatId) {
        try {
            if (!this.backupManager) {
                await this.bot.sendMessage(chatId, '❌ نظام النسخ الاحتياطي غير متاح');
                return;
            }

            const backupHistory = await this.backupManager.getBackupHistory();

            if (backupHistory.length === 0) {
                await this.bot.sendMessage(chatId, '📂 لا توجد نسخ احتياطية للحذف');
                return;
            }

            // طلب تأكيد الحذف
            const confirmMessage = `⚠️ **تأكيد حذف جميع النسخ الاحتياطية**\n\n` +
                `📊 **سيتم حذف:**\n` +
                `• عدد النسخ: ${backupHistory.length}\n` +
                `• الحجم الإجمالي: ${Math.round(backupHistory.reduce((total, backup) => total + (backup.compressedSize || backup.size || 0), 0) / 1024)} KB\n\n` +
                `❌ **تحذير:** هذا الإجراء لا يمكن التراجع عنه!`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '✅ نعم، احذف الكل', callback_data: 'admin_confirm_delete_all_backups' },
                        { text: '❌ إلغاء', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, confirmMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض تأكيد حذف النسخ:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض تأكيد الحذف');
        }
    }

    // تأكيد حذف جميع النسخ
    async confirmDeleteAllBackups(chatId) {
        try {
            const statusMsg = await this.bot.sendMessage(chatId, '🗑️ جاري حذف جميع النسخ الاحتياطية...');

            // استخدام الدالة الجديدة لحذف جميع النسخ
            const result = await this.backupManager.deleteAllBackups();

            await this.bot.deleteMessage(chatId, statusMsg.message_id);

            const message = `🗑️ **تم حذف جميع النسخ الاحتياطية**\n\n` +
                `📊 **النتائج:**\n` +
                `• النسخ المحذوفة: ${result.deletedCount}\n` +
                `• النسخ المتبقية: 0\n\n` +
                `✅ تم تنظيف النظام بالكامل`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '💾 إنشاء نسخة جديدة', callback_data: 'admin_create_backup' },
                        { text: '🔙 إدارة النسخ', callback_data: 'admin_backup' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في حذف جميع النسخ:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في حذف النسخ الاحتياطية');
        }
    }

    // إرسال النسخة التلقائية للمدير
    async sendAutoBackupToAdmin(backup) {
        try {
            const adminIds = process.env.ADMIN_IDS ?
                process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
                [];

            if (adminIds.length === 0) {
                console.log('⚠️ لا توجد معرفات مدراء لإرسال النسخة التلقائية');
                return;
            }

            const backupPath = require('path').join('./backups/', backup.filename);
            if (!require('fs').existsSync(backupPath)) {
                console.error('❌ ملف النسخة التلقائية غير موجود:', backupPath);
                return;
            }

            const backupDate = new Date(backup.timestamp);
            const caption = `🤖 **نسخة احتياطية تلقائية**\n\n` +
                `📁 **الملف:** ${backup.filename}\n` +
                `📊 **الحجم:** ${Math.round(backup.compressedSize / 1024)} KB\n` +
                `📅 **التاريخ:** ${backupDate.toLocaleDateString('ar-EG')}\n` +
                `⏰ **الوقت:** ${backupDate.toLocaleTimeString('ar-EG')}\n` +
                `🔄 **النوع:** تلقائي\n` +
                `🆔 **المعرف:** ${backup.id}\n\n` +
                `💾 تم إنشاؤها تلقائياً بواسطة النظام\n` +
                `🔒 تحتوي على: قاعدة البيانات + الإعدادات\n` +
                `📦 يمكن استعادة النظام بالكامل من هذه النسخة`;

            // إرسال النسخة لجميع المدراء
            for (const adminId of adminIds) {
                try {
                    const autoBackupMessage = await this.bot.sendDocument(adminId, backupPath, {
                        caption: caption + `\n\n💾 هذا الملف محفوظ ولن يتم حذفه من المحادثة`
                    });
                    console.log(`📤 تم إرسال النسخة التلقائية للمدير ${adminId}`);

                    // حفظ معرف رسالة النسخة التلقائية لمنع حذفها
                    if (!this.backupMessages) this.backupMessages = new Set();
                    this.backupMessages.add(autoBackupMessage.message_id);
                } catch (error) {
                    console.error(`❌ خطأ في إرسال النسخة التلقائية للمدير ${adminId}:`, error);
                }
            }

        } catch (error) {
            console.error('❌ خطأ في إرسال النسخة التلقائية للمدراء:', error);
        }
    }

    // إشعار المدراء بخطأ في النسخ التلقائي
    async notifyAdminBackupError(error) {
        try {
            const adminIds = process.env.ADMIN_IDS ?
                process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
                [];

            if (adminIds.length === 0) {
                return;
            }

            const errorMessage = `⚠️ **خطأ في النسخ الاحتياطي التلقائي**\n\n` +
                `❌ **الخطأ:** ${error.message || error}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `🔧 يرجى التحقق من النظام`;

            // إرسال الإشعار لجميع المدراء
            for (const adminId of adminIds) {
                try {
                    await this.bot.sendMessage(adminId, errorMessage, {
                        parse_mode: 'Markdown'
                    });
                } catch (sendError) {
                    console.error(`❌ خطأ في إرسال إشعار الخطأ للمدير ${adminId}:`, sendError);
                }
            }

        } catch (error) {
            console.error('❌ خطأ في إشعار المدراء بخطأ النسخ التلقائي:', error);
        }
    }

    // إعادة تشغيل النظام
    async restartSystem(chatId = null) {
        try {
            if (chatId) {
                await this.bot.sendMessage(chatId, '🔄 جاري إعادة تشغيل النظام...\n\n⏳ سيتم إعادة تشغيل النظام خلال 3 ثوانٍ');
            }

            // إنشاء نسخة احتياطية قبل إعادة التشغيل (إذا لم تكن استعادة)
            if (this.backupManager && chatId) {
                await this.backupManager.createBackup();
            }

            // إيقاف جميع البوتات مع حفظ الحالة إذا كانت متاحة
            if (this.botManager && this.botManager.stopAllBotsWithState) {
                await this.botManager.stopAllBotsWithState();
            } else if (this.botManager && this.botManager.stopAllBots) {
                await this.botManager.stopAllBots();
            }

            // إعادة تشغيل النظام بعد 3 ثوانٍ
            setTimeout(() => {
                console.log('🔄 إعادة تشغيل النظام...');

                // استخدام child_process لإعادة التشغيل
                const child = spawn('node', ['index.js'], {
                    detached: true,
                    stdio: 'ignore',
                    cwd: process.cwd()
                });

                child.unref();
                console.log('✅ تم بدء عملية جديدة للنظام');
                process.exit(0);
            }, 3000);

        } catch (error) {
            console.error('❌ خطأ في إعادة تشغيل النظام:', error);
            if (chatId) {
                await this.bot.sendMessage(chatId, '❌ حدث خطأ في إعادة تشغيل النظام');
            }
            // في حالة الخطأ، إعادة التشغيل العادي
            setTimeout(() => {
                process.exit(0);
            }, 1000);
        }
    }

    // إعادة تشغيل فوري بدون معالجة إضافية (للاستعادة)
    forceRestart() {
        try {
            console.log('🔄 إعادة تشغيل فوري للنظام...');

            // إيقاف جميع المعالجات
            if (this.bot) {
                this.bot.removeAllListeners();
            }

            // إغلاق اتصالات قاعدة البيانات
            if (this.db && this.db.close) {
                this.db.close();
            }

            // إيقاف البوتات
            if (this.botManager && this.botManager.stopAllBots) {
                this.botManager.stopAllBots();
            }

            // إعادة التشغيل الفوري
            const child = spawn('node', ['index.js'], {
                detached: true,
                stdio: 'ignore',
                cwd: process.cwd()
            });

            child.unref();
            console.log('✅ تم بدء عملية جديدة للنظام (إعادة تشغيل فوري)');
            process.exit(0);

        } catch (error) {
            console.error('❌ خطأ في إعادة التشغيل الفوري:', error);
            // في حالة الخطأ، إنهاء العملية مباشرة
            process.exit(1);
        }
    }

    // دوال مؤقتة للأوامر التي لم يتم تنفيذها بعد
    async showUserManagement(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showBotManagement(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showSystemLogs(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showSystemSettings(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showAlertManagement(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showBanManagement(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    async showDetailedReports(chatId) {
        await this.bot.sendMessage(chatId, '🚧 هذه الميزة قيد التطوير...');
        await this.showAdminPanel(chatId);
    }

    // عرض إعدادات حد البوتات
    async showBotLimitSettings(chatId) {
        try {
            const currentLimit = await this.db.getSetting('max_bots_per_user', 5);
            const totalUsers = await this.db.getTotalUsers();
            const totalBots = await this.db.getTotalBots();

            const message = `🔢 **إعدادات حد البوتات**\n\n` +
                `📊 **الوضع الحالي:**\n` +
                `• الحد الأقصى الحالي: ${currentLimit} بوت لكل مستخدم\n` +
                `• إجمالي المستخدمين: ${totalUsers}\n` +
                `• إجمالي البوتات: ${totalBots}\n` +
                `• متوسط البوتات لكل مستخدم: ${totalUsers > 0 ? Math.round(totalBots / totalUsers * 100) / 100 : 0}\n\n` +
                `⚠️ **تحذير:** تقليل الحد سيؤدي إلى حذف البوتات الزائدة (الأقدم) لجميع المستخدمين\n\n` +
                `🔧 **اختر الحد الجديد:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '1️⃣ بوت واحد', callback_data: 'set_bot_limit_1' },
                        { text: '2️⃣ بوتان', callback_data: 'set_bot_limit_2' },
                        { text: '3️⃣ ثلاثة بوتات', callback_data: 'set_bot_limit_3' }
                    ],
                    [
                        { text: '4️⃣ أربعة بوتات', callback_data: 'set_bot_limit_4' },
                        { text: '5️⃣ خمسة بوتات', callback_data: 'set_bot_limit_5' },
                        { text: '🔟 عشرة بوتات', callback_data: 'set_bot_limit_10' }
                    ],
                    [
                        { text: '🔢 رقم مخصص', callback_data: 'set_bot_limit_custom' }
                    ],
                    [
                        { text: '🔙 العودة للوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض إعدادات حد البوتات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض إعدادات حد البوتات');
        }
    }

    // معالجة تحديد حد البوتات
    async handleBotLimitSetting(query) {
        const chatId = query.message.chat.id;
        const userId = query.from.id;
        const data = query.data;

        // التحقق من صلاحيات الأدمن
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];

        if (!adminIds.includes(userId)) {
            await this.bot.sendMessage(chatId, '❌ ليس لديك صلاحية للوصول لهذه الميزة');
            return;
        }

        try {
            if (data === 'set_bot_limit_custom') {
                // طلب إدخال رقم مخصص
                await this.requestCustomBotLimit(chatId, userId);
            } else {
                // استخراج الرقم من البيانات
                const limit = parseInt(data.replace('set_bot_limit_', ''));
                await this.setBotLimit(chatId, limit);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة تحديد حد البوتات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في معالجة طلبك');
        }
    }

    // طلب إدخال حد مخصص
    async requestCustomBotLimit(chatId, userId) {
        const message = `🔢 **إدخال حد مخصص**\n\n` +
            `📝 أرسل رقماً بين 1 و 50 لتحديد الحد الأقصى للبوتات لكل مستخدم\n\n` +
            `⚠️ **تحذير:** تقليل الحد سيؤدي إلى حذف البوتات الزائدة (الأقدم) لجميع المستخدمين`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '❌ إلغاء', callback_data: 'admin_bot_limit' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);

        // حفظ حالة انتظار الإدخال
        this.userSessions.set(userId, {
            step: 'waiting_bot_limit',
            chatId: chatId,
            timestamp: Date.now()
        });
    }

    // تعيين حد البوتات
    async setBotLimit(chatId, newLimit) {
        try {
            if (isNaN(newLimit) || newLimit < 1 || newLimit > 50) {
                await this.bot.sendMessage(chatId, '❌ الرقم يجب أن يكون بين 1 و 50');
                return;
            }

            const currentLimit = await this.db.getSetting('max_bots_per_user', 5);

            // حفظ الحد الجديد
            await this.db.setSetting('max_bots_per_user', newLimit, 'number', 'الحد الأقصى لعدد البوتات لكل مستخدم', 'limits');

            let message = `✅ **تم تحديث حد البوتات بنجاح**\n\n` +
                `🔢 **الحد الجديد:** ${newLimit} بوت لكل مستخدم\n` +
                `📊 **الحد السابق:** ${currentLimit} بوت لكل مستخدم\n\n`;

            // إذا كان الحد الجديد أقل من السابق، نحتاج لحذف البوتات الزائدة
            if (newLimit < currentLimit) {
                const deletedCount = await this.enforceNewBotLimit(newLimit);
                message += `🗑️ **تم حذف ${deletedCount} بوت زائد من جميع المستخدمين**\n\n`;
            }

            message += `💡 **ملاحظة:** الحد الجديد سيطبق على جميع المستخدمين فوراً`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔙 العودة لإعدادات البوتات', callback_data: 'admin_bot_limit' }
                    ],
                    [
                        { text: '🏠 لوحة الأدمن', callback_data: 'admin_panel' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في تعيين حد البوتات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في تحديث حد البوتات');
        }
    }

    // معالجة إدخال حد البوتات المخصص
    async handleCustomBotLimitInput(msg, session) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;
        const text = msg.text.trim();

        try {
            // التحقق من أن النص رقم صحيح
            const limit = parseInt(text);

            if (isNaN(limit)) {
                await this.bot.sendMessage(chatId, '❌ يرجى إدخال رقم صحيح بين 1 و 50');
                return;
            }

            if (limit < 1 || limit > 50) {
                await this.bot.sendMessage(chatId, '❌ الرقم يجب أن يكون بين 1 و 50');
                return;
            }

            // حذف الجلسة
            this.userSessions.delete(userId);

            // تطبيق الحد الجديد
            await this.setBotLimit(chatId, limit);

        } catch (error) {
            console.error('❌ خطأ في معالجة إدخال حد البوتات المخصص:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في معالجة الرقم المدخل');

            // حذف الجلسة في حالة الخطأ
            this.userSessions.delete(userId);
        }
    }

    // تطبيق الحد الجديد وحذف البوتات الزائدة
    async enforceNewBotLimit(newLimit) {
        try {
            let totalDeleted = 0;

            // الحصول على جميع المستخدمين
            const users = await this.db.getAllUsers();

            for (const user of users) {
                // الحصول على بوتات المستخدم مرتبة حسب تاريخ الإنشاء (الأقدم أولاً)
                const userBots = await this.db.getUserBots(user.telegram_id);

                if (userBots.length > newLimit) {
                    // حساب عدد البوتات التي يجب حذفها
                    const botsToDelete = userBots.length - newLimit;

                    // ترتيب البوتات حسب تاريخ الإنشاء (الأقدم أولاً)
                    userBots.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

                    // حذف البوتات الأقدم
                    for (let i = 0; i < botsToDelete; i++) {
                        const botToDelete = userBots[i];

                        // إيقاف البوت إذا كان يعمل
                        if (this.botManager.activeBots.has(botToDelete.id)) {
                            await this.botManager.stopBot(botToDelete.id);
                        }

                        // حذف البوت من قاعدة البيانات
                        await this.db.deleteBot(botToDelete.id);
                        totalDeleted++;

                        console.log(`🗑️ تم حذف البوت ${botToDelete.bot_name} للمستخدم ${user.telegram_id} (تطبيق الحد الجديد)`);
                    }

                    // إرسال إشعار للمستخدم
                    if (botsToDelete > 0) {
                        try {
                            const message = `⚠️ **تحديث حد البوتات**\n\n` +
                                `تم تحديث الحد الأقصى للبوتات إلى ${newLimit} بوت لكل مستخدم.\n\n` +
                                `🗑️ **تم حذف ${botsToDelete} من بوتاتك الأقدم:**\n` +
                                userBots.slice(0, botsToDelete).map(bot => `• ${bot.bot_name}`).join('\n') + '\n\n' +
                                `💡 يمكنك إنشاء بوتات جديدة في أي وقت.`;

                            await this.bot.sendMessage(user.telegram_id, message, {
                                parse_mode: 'Markdown'
                            });
                        } catch (notificationError) {
                            console.error(`❌ خطأ في إرسال إشعار للمستخدم ${user.telegram_id}:`, notificationError);
                        }
                    }
                }
            }

            return totalDeleted;

        } catch (error) {
            console.error('❌ خطأ في تطبيق الحد الجديد للبوتات:', error);
            return 0;
        }
    }

    // ==========================================
    // دوال عرض القوائم
    // ==========================================

    // عرض القائمة الرئيسية
    async showMainMenu(chatId, userId = null) {
        const menuMessage = `🏠 **القائمة الرئيسية**\n\n` +
            `🎮 **نظام بوت ماينكرافت المتقدم**\n\n` +
            `اختر ما تريد القيام به:`;

        // التحقق من صلاحيات الأدمن
        const adminIds = process.env.ADMIN_IDS ?
            process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
            [];
        const isAdmin = userId && adminIds.includes(userId);

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' },
                    { text: '📋 بوتاتي', callback_data: 'my_bots' }
                ],
                [
                    { text: '📊 الإحصائيات', callback_data: 'stats' },
                    { text: '❓ المساعدة', callback_data: 'help' }
                ],
                [
                    { text: '⚙️ الإعدادات', callback_data: 'settings' }
                ]
            ]
        };

        // إضافة زر الأدمن للمدراء فقط
        if (isAdmin) {
            keyboard.inline_keyboard.push([
                { text: '👑 لوحة الأدمن', callback_data: 'admin_panel' }
            ]);
        }

        const sentMessage = await this.bot.sendMessage(chatId, menuMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // عرض قائمة إنشاء بوت جديد
    async showNewBotMenu(chatId, userId = null) {
        // الحصول على userId إذا لم يتم تمريره
        if (!userId) {
            userId = chatId; // في حالة الرسائل المباشرة
        }

        // التحقق من وجود بوت للمستخدم
        try {
            const userBots = await this.db.getUserBots(userId);
            const maxBotsPerUser = await this.db.getSetting('max_bots_per_user', 5);

            if (userBots.length >= maxBotsPerUser) {
                const limitMessage = `🚫 **لا يمكن إنشاء بوت جديد**\n\n` +
                    `❌ **السبب:** وصلت للحد الأقصى (${userBots.length}/${maxBotsPerUser})\n\n` +
                    `🤖 **البوتات الحالية:**\n` +
                    userBots.slice(0, 3).map(bot =>
                        `• ${bot.bot_name} - ${bot.server_host}:${bot.server_port}`
                    ).join('\n') +
                    (userBots.length > 3 ? `\n• ... و ${userBots.length - 3} بوت آخر` : '') +
                    `\n\n💡 **لإنشاء بوت جديد:**\n` +
                    `1️⃣ احذف أحد البوتات الحالية أولاً\n` +
                    `2️⃣ ثم أنشئ بوت جديد\n\n` +
                    `📋 **اضغط "بوتاتي" لإدارة البوتات**`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '⚙️ إدارة البوت الحالي', callback_data: `bot_manage_${existingBot.id}` }
                        ],
                        [
                            { text: '📋 بوتاتي', callback_data: 'my_bots' }
                        ],
                        [
                            { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, limitMessage, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
                return;
            }
        } catch (error) {
            console.error('خطأ في التحقق من بوتات المستخدم:', error);
        }

        // إذا لم يكن لديه بوت، اعرض قائمة الإنشاء
        const newBotMessage = `🤖 **إنشاء بوت جديد**\n\n` +
            `اختر نوع البوت الذي تريد إنشاؤه:\n\n` +
            `☕ **Java Edition:** للسيرفرات التقليدية\n` +
            `🪨 **Bedrock Edition:** للجوال والكونسول\n\n` +
            `📋 **الإصدارات المدعومة:**\n` +
            `☕ Java: ${this.supportedVersions.java.slice(0, 3).join(', ')}\n` +
            `🪨 Bedrock: ${this.supportedVersions.bedrock.slice(0, 3).join(', ')}\n\n` +
            `⚠️ **ملاحظة:** يمكنك إنشاء بوت واحد فقط`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '☕ Java Edition', callback_data: 'new_bot_java' },
                    { text: '🪨 Bedrock Edition', callback_data: 'new_bot_bedrock' }
                ],
                [
                    { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, newBotMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // عرض بوتات المستخدم
    async showMyBots(chatId, userId) {
        try {
            const userBots = await this.db.getUserBots(userId);

            if (userBots.length === 0) {
                const noBotMessage = `📋 **بوتاتي**\n\n` +
                    `🤖 لا توجد بوتات حالياً\n\n` +
                    `💡 **ابدأ بإنشاء بوتك الأول!**`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '🚀 إنشاء بوت جديد', callback_data: 'new_bot' }
                        ],
                        [
                            { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, noBotMessage, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
                return;
            }

            // عرض قائمة البوتات
            let botsList = `📋 **بوتاتي (${userBots.length}/${config.bots.maxBotsPerUser})**\n\n`;

            const keyboard = { inline_keyboard: [] };

            for (const bot of userBots) {
                const isActive = this.botManager.activeBots.has(bot.id);
                const statusIcon = isActive ? '🟢' : '🔴';
                const statusText = isActive ? 'متصل' : 'متوقف';

                botsList += `${statusIcon} **${bot.bot_name}**\n`;
                botsList += `   🌐 ${bot.server_host}:${bot.server_port}\n`;
                botsList += `   📦 ${bot.edition} ${bot.minecraft_version}\n`;
                botsList += `   📊 ${statusText}\n\n`;

                // إضافة أزرار للبوت
                const botButtons = [
                    {
                        text: `${statusIcon} ${bot.bot_name}`,
                        callback_data: `bot_manage_${bot.id}`
                    }
                ];

                keyboard.inline_keyboard.push(botButtons);
            }

            // أزرار إضافية
            keyboard.inline_keyboard.push([
                { text: '🚀 إنشاء بوت جديد', callback_data: 'new_bot' }
            ]);
            keyboard.inline_keyboard.push([
                { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
            ]);

            const sentMessage = await this.bot.sendMessage(chatId, botsList, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض البوتات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض البوتات');
        }
    }

    // عرض الإحصائيات
    async showStats(chatId, userId) {
        try {
            const userBots = await this.db.getUserBots(userId);
            const activeBots = userBots.filter(bot => this.botManager.activeBots.has(bot.id));
            const maxBotsPerUser = await this.db.getSetting('max_bots_per_user', 5);

            const statsMessage = `📊 **الإحصائيات**\n\n` +
                `🤖 **البوتات:**\n` +
                `   • المجموع: ${userBots.length}/${maxBotsPerUser}\n` +
                `   • النشطة: ${activeBots.length}\n` +
                `   • المتوقفة: ${userBots.length - activeBots.length}\n\n` +
                `📈 **الأداء:**\n` +
                `   • وقت التشغيل: ${Math.floor(process.uptime())} ثانية\n` +
                `   • استخدام الذاكرة: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB\n\n` +
                `🎮 **الإصدارات المدعومة:**\n` +
                `   • Java: ${this.supportedVersions.java.length} إصدار\n` +
                `   • Bedrock: ${this.supportedVersions.bedrock.length} إصدار`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '📋 بوتاتي', callback_data: 'my_bots' },
                        { text: '🚀 إنشاء بوت', callback_data: 'new_bot' }
                    ],
                    [
                        { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, statsMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض الإحصائيات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض الإحصائيات');
        }
    }

    // عرض الإعدادات
    async showSettings(chatId, userId) {
        try {
            const user = await this.db.getUser(userId);

            const settingsMessage = `⚙️ **الإعدادات**\n\n` +
                `👤 **معلومات المستخدم:**\n` +
                `   • المعرف: ${userId}\n` +
                `   • اللغة: ${user?.language_code || 'ar'}\n` +
                `   • تاريخ التسجيل: ${user?.created_at ? new Date(user.created_at).toLocaleDateString('ar-EG') : 'غير محدد'}\n\n` +
                `🎮 **إعدادات البوتات:**\n` +
                `   • الحد الأقصى: ${config.bots.maxBotsPerUser} بوت\n` +
                `   • إعادة الاتصال التلقائي: مفعل\n` +
                `   • المراقبة: مفعلة\n\n` +
                `🔧 **إعدادات النظام:**\n` +
                `   • الإصدار: 2.0.0\n` +
                `   • البيئة: ${process.env.NODE_ENV || 'production'}\n` +
                `   • النسخ الاحتياطي: مفعل`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🌐 تغيير اللغة', callback_data: 'change_language' },
                        { text: '🔔 الإشعارات', callback_data: 'notifications' }
                    ],
                    [
                        { text: '📊 الإحصائيات', callback_data: 'stats' },
                        { text: '❓ المساعدة', callback_data: 'help' }
                    ],
                    [
                        { text: '🔙 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, settingsMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض الإعدادات:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض الإعدادات');
        }
    }

    // عرض المساعدة
    async showHelp(chatId) {
        const helpMessage = `❓ **دليل الاستخدام**\n\n` +
            `🤖 **إدارة البوتات:**\n` +
            `• /newbot - إنشاء بوت جديد\n` +
            `• /mybots - عرض بوتاتك\n` +
            `• /stats - إحصائيات مفصلة\n\n` +
            `⚙️ **الأوامر العامة:**\n` +
            `• /start - البداية والقائمة الرئيسية\n` +
            `• /menu - القائمة الرئيسية\n` +
            `• /help - هذه المساعدة\n\n` +
            `🎮 **أنواع البوتات المدعومة:**\n` +
            `☕ **Java Edition:** للسيرفرات التقليدية\n` +
            `🪨 **Bedrock Edition:** للجوال والكونسول\n\n` +
            `🔧 **الميزات المتقدمة:**\n` +
            `• إعادة اتصال تلقائي\n` +
            `• مراقبة الحالة\n` +
            `• إرسال الرسائل والأوامر\n` +
            `• إحصائيات مفصلة\n` +
            `• تنبيهات ذكية\n\n` +
            `💡 **نصائح:**\n` +
            `• استخدم أسماء فريدة للبوتات\n` +
            `• تأكد من صحة عنوان السيرفر\n` +
            `• اختر الإصدار المناسب للسيرفر`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت', callback_data: 'new_bot' },
                    { text: '📋 بوتاتي', callback_data: 'my_bots' }
                ],
                [
                    { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, helpMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة الاستعلامات المعقدة
    async handleComplexCallbackQuery(query) {
        const chatId = query.message.chat.id;
        const userId = query.from.id;
        const data = query.data;

        // معالجة إنشاء البوتات - بدء العملية مباشرة
        if (data.startsWith('new_bot_')) {
            const edition = data.replace('new_bot_', '');
            await this.startBotCreationProcess(chatId, userId, edition);
            return;
        }

        // معالجة إدارة البوتات
        if (data.startsWith('bot_manage_')) {
            const botId = parseInt(data.replace('bot_manage_', ''));
            await this.handleBotManagement(chatId, userId, botId);
            return;
        }

        // معالجة إجراءات البوتات
        if (data.startsWith('bot_action_')) {
            const parts = data.split('_');
            const botId = parseInt(parts[2]);
            const action = parts[3];
            await this.handleBotAction(chatId, userId, botId, action);
            return;
        }

        // معالجة تأكيد الحذف
        if (data.startsWith('confirm_delete_')) {
            const botId = parseInt(data.replace('confirm_delete_', ''));
            await this.confirmDeleteBot(chatId, userId, botId);
            return;
        }

        // معالجة إلغاء إنشاء البوت
        if (data === 'cancel_bot_creation') {
            await this.cancelBotCreation(chatId, userId);
            return;
        }

        // معالجة تغيير عنوان السيرفر
        if (data === 'change_server_host') {
            await this.changeServerHost(chatId, userId);
            return;
        }

        // معالجة أوامر الأدمن الجديدة
        if (data.startsWith('admin_')) {
            await this.handleAdminCallbacks(query);
            return;
        }

        // معالجة تحديد حد البوتات
        if (data.startsWith('set_bot_limit_')) {
            await this.handleBotLimitSetting(query);
            return;
        }

        // معالجة اختيار الإصدار
        if (data.startsWith('select_version_')) {
            const version = data.replace('select_version_', '');
            const session = this.userSessions.get(userId);
            if (session && session.step === 'minecraft_version') {
                await this.handleVersionSelection(chatId, userId, version, session);
            }
            return;
        }

        // إرسال رسالة خطأ للاستعلامات غير المعروفة
        await this.bot.sendMessage(chatId, '❌ استعلام غير معروف');
    }

    // بدء عملية إنشاء البوت مباشرة
    async startBotCreationProcess(chatId, userId, edition) {
        const editionName = edition === 'java' ? 'Java Edition' : 'Bedrock Edition';
        const editionIcon = edition === 'java' ? '☕' : '🪨';

        // إنشاء جلسة للمستخدم
        const session = {
            state: 'creating_bot',
            step: 'minecraft_version',
            edition: edition,
            data: {},
            startTime: Date.now()
        };

        this.userSessions.set(userId, session);

        const versions = this.supportedVersions[edition];

        const message = `${editionIcon} **إنشاء بوت ${editionName}**\n\n` +
            `📦 **الخطوة 1/5: اختيار إصدار السيرفر**\n\n` +
            `📋 **اختر الإصدار المناسب لسيرفرك:**\n` +
            `💡 **تأكد من اختيار نفس إصدار السيرفر**`;

        const keyboard = { inline_keyboard: [] };

        // إضافة أزرار الإصدارات (2 في كل صف)
        for (let i = 0; i < versions.length; i += 2) {
            const row = [];
            for (let j = i; j < Math.min(i + 2, versions.length); j++) {
                row.push({
                    text: `📦 ${versions[j]}`,
                    callback_data: `select_version_${versions[j]}`
                });
            }
            keyboard.inline_keyboard.push(row);
        }

        keyboard.inline_keyboard.push([
            { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
        ]);

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة مدخلات إنشاء البوت
    async handleBotCreationInput(msg, session) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;
        const text = msg.text.trim();

        try {
            switch (session.step) {
                case 'server_host':
                    await this.handleServerHostInput(chatId, userId, text, session);
                    break;
                case 'server_port':
                    await this.handleServerPortInput(chatId, userId, text, session);
                    break;
                case 'bot_name':
                    await this.handleBotNameInput(chatId, userId, text, session);
                    break;
                default:
                    await this.bot.sendMessage(chatId, '❌ خطأ في عملية الإنشاء. يرجى البدء من جديد.');
                    this.userSessions.delete(userId);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة مدخلات إنشاء البوت:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ. يرجى المحاولة مرة أخرى.');
            this.userSessions.delete(userId);
        }
    }

    // معالجة عنوان السيرفر (الخطوة الثانية)
    async handleServerHostInput(chatId, userId, host, session) {
        // التحقق من صحة العنوان
        if (!host || host.length < 3) {
            await this.bot.sendMessage(chatId, '❌ عنوان السيرفر قصير جداً. يرجى إدخال عنوان صحيح.');
            return;
        }

        // التحقق من صيغة العنوان
        const hostRegex = /^[a-zA-Z0-9.-]+$/;
        if (!hostRegex.test(host)) {
            await this.bot.sendMessage(chatId, '❌ عنوان السيرفر يحتوي على رموز غير صحيحة. استخدم أحرف وأرقام ونقاط فقط.');
            return;
        }

        session.data.server_host = host;
        session.step = 'server_port';

        const editionIcon = session.edition === 'java' ? '☕' : '🪨';

        const message = `${editionIcon} **إنشاء البوت - ${session.edition === 'java' ? 'Java' : 'Bedrock'}**\n\n` +
            `✅ **الإصدار:** ${session.data.minecraft_version}\n` +
            `✅ **عنوان السيرفر:** ${host}\n\n` +
            `🔌 **الخطوة 3/5: رقم المنفذ (Port)**\n\n` +
            `📝 **أرسل رقم المنفذ:**\n` +
            `• يجب أن يكون بين 1 و 65535\n` +
            `• مثال: 25565 (Java) أو 19132 (Bedrock)\n` +
            `• تحقق من إعدادات السيرفر للحصول على الرقم الصحيح\n\n` +
            `💡 **أرسل فقط الرقم (مثال: 25565)**`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة رقم المنفذ (الخطوة الثالثة)
    async handleServerPortInput(chatId, userId, portText, session) {
        const port = parseInt(portText);

        // التحقق من صحة المنفذ
        if (isNaN(port) || port < 1 || port > 65535) {
            await this.bot.sendMessage(chatId, '❌ رقم المنفذ غير صحيح. يجب أن يكون بين 1 و 65535.\n\n📝 أرسل رقم المنفذ مرة أخرى:');
            return;
        }

        // أولاً: التحقق من وجود سيرفر مكرر للمستخدم الحالي
        const existingBotForUser = await this.db.getBotByServerForUser(userId, session.data.server_host, port);
        if (existingBotForUser) {
            const duplicateMessage = `🚫 **سيرفر مكرر لديك!**\n\n` +
                `❌ **المشكلة:** لديك بوت آخر متصل بنفس السيرفر\n\n` +
                `🌐 **السيرفر:** ${session.data.server_host}:${port}\n` +
                `🤖 **البوت الموجود:** ${existingBotForUser.bot_name}\n\n` +
                `💡 **الحلول:**\n` +
                `1️⃣ استخدم عنوان سيرفر مختلف\n` +
                `2️⃣ استخدم منفذ مختلف\n` +
                `3️⃣ احذف البوت الموجود أولاً\n\n` +
                `📝 **أرسل رقم منفذ مختلف:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 تغيير عنوان السيرفر', callback_data: 'change_server_host' }
                    ],
                    [
                        { text: '📋 بوتاتي', callback_data: 'my_bots' }
                    ],
                    [
                        { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                    ]
                ]
            };

            await this.bot.sendMessage(chatId, duplicateMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });
            return;
        }

        // ثانياً: التحقق من وجود سيرفر لمستخدمين آخرين
        const existingBotForOthers = await this.db.getBotByServerForOtherUsers(userId, session.data.server_host, port);
        if (existingBotForOthers) {
            const duplicateMessage = `⚠️ **سيرفر مستخدم من قبل مستخدم آخر!**\n\n` +
                `❌ **المشكلة:** يوجد بوت آخر متصل بنفس السيرفر\n\n` +
                `🌐 **السيرفر:** ${session.data.server_host}:${port}\n` +
                `🤖 **البوت الموجود:** ${existingBotForOthers.bot_name}\n` +
                `👤 **المالك:** مستخدم آخر\n\n` +
                `💡 **الحلول:**\n` +
                `1️⃣ تأكد من صحة عنوان السيرفر\n` +
                `2️⃣ تأكد من صحة رقم المنفذ\n` +
                `3️⃣ استخدم سيرفر مختلف\n\n` +
                `📝 **أرسل عنوان السيرفر مرة أخرى:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 تغيير عنوان السيرفر', callback_data: 'change_server_host' }
                    ],
                    [
                        { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                    ]
                ]
            };

            await this.bot.sendMessage(chatId, duplicateMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            // العودة لخطوة إدخال عنوان السيرفر
            session.step = 'server_host';
            return;
        }

        session.data.server_port = port;
        session.step = 'bot_name';

        const editionIcon = session.edition === 'java' ? '☕' : '🪨';

        const message = `${editionIcon} **إنشاء البوت - ${session.edition === 'java' ? 'Java' : 'Bedrock'}**\n\n` +
            `✅ **الإصدار:** ${session.data.minecraft_version}\n` +
            `✅ **عنوان السيرفر:** ${session.data.server_host}\n` +
            `✅ **المنفذ:** ${port}\n\n` +
            `🤖 **الخطوة 4/5: اسم البوت**\n\n` +
            `📝 **أرسل اسم البوت:**\n` +
            `• يجب أن يكون 3 أحرف أو أكثر\n` +
            `• يمكن استخدام الأحرف والأرقام\n` +
            `• يمكن استخدام الفراغات والفواصل\n` +
            `• يمكن استخدام النقاط والشرطات\n\n` +
            `💡 **أمثلة:** بوت الخادم، My Bot 2024، Server-Helper`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة اسم البوت (الخطوة الرابعة والأخيرة)
    async handleBotNameInput(chatId, userId, botName, session) {
        // تنظيف الاسم من الفراغات الزائدة
        botName = botName.trim();

        // التحقق من طول الاسم
        if (!botName || botName.length < 3) {
            await this.bot.sendMessage(chatId, '❌ اسم البوت قصير جداً. يجب أن يكون 3 أحرف أو أكثر.');
            return;
        }

        if (botName.length > 50) {
            await this.bot.sendMessage(chatId, '❌ اسم البوت طويل جداً. يجب أن يكون 50 حرف أو أقل.');
            return;
        }

        // التحقق من صحة الاسم - السماح بالأحرف والأرقام والفراغات والفواصل والشرطات
        const nameRegex = /^[a-zA-Z0-9\u0600-\u06FF\s,._-]+$/;
        if (!nameRegex.test(botName)) {
            await this.bot.sendMessage(chatId, '❌ اسم البوت يحتوي على رموز غير مسموحة.\n\n' +
                '✅ **المسموح:**\n' +
                '• الأحرف الإنجليزية والعربية\n' +
                '• الأرقام\n' +
                '• الفراغات\n' +
                '• الفواصل (,)\n' +
                '• النقاط (.)\n' +
                '• الشرطات (-_)\n\n' +
                '📝 أرسل اسماً جديداً للبوت:', { parse_mode: 'Markdown' });
            return;
        }

        // التحقق من عدم تكرار الاسم للمستخدم الحالي فقط
        const existingBotForUser = await this.db.getBotByNameForUser(userId, botName);
        if (existingBotForUser) {
            const duplicateMessage = `🚫 **اسم البوت مكرر!**\n\n` +
                `❌ **المشكلة:** لديك بوت آخر بنفس الاسم\n\n` +
                `🤖 **الاسم المكرر:** ${botName}\n` +
                `🌐 **السيرفر:** ${existingBotForUser.server_host}:${existingBotForUser.server_port}\n\n` +
                `💡 **اقتراحات أسماء:**\n` +
                `• ${botName} 2\n` +
                `• ${botName}_جديد\n` +
                `• بوت_${botName}\n` +
                `• ${botName}_v2\n\n` +
                `📝 **أرسل اسماً جديداً للبوت:**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                    ]
                ]
            };

            await this.bot.sendMessage(chatId, duplicateMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });
            return;
        }

        session.data.bot_name = botName;

        // إنشاء البوت في قاعدة البيانات مباشرة
        await this.createBotInDatabase(chatId, userId, session);
    }

    // معالجة اختيار الإصدار (الخطوة الأولى)
    async handleVersionSelection(chatId, userId, version, session) {
        // التحقق من صحة الإصدار
        const validVersions = this.supportedVersions[session.edition];
        if (!validVersions.includes(version)) {
            await this.bot.sendMessage(chatId, '❌ إصدار غير مدعوم. يرجى اختيار إصدار من القائمة.');
            return;
        }

        session.data.minecraft_version = version;
        session.step = 'server_host';

        const editionIcon = session.edition === 'java' ? '☕' : '🪨';

        const message = `${editionIcon} **إنشاء البوت - ${session.edition === 'java' ? 'Java' : 'Bedrock'}**\n\n` +
            `✅ **الإصدار:** ${version}\n\n` +
            `🌐 **الخطوة 2/5: عنوان السيرفر**\n\n` +
            `📝 **أرسل عنوان السيرفر (IP أو رابط):**\n` +
            `• مثال: play.hypixel.net\n` +
            `• مثال: *************\n` +
            `• مثال: mc.server.com\n\n` +
            `💡 **تأكد من صحة العنوان قبل الإرسال**`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // إنشاء البوت في قاعدة البيانات
    async createBotInDatabase(chatId, userId, session) {
        try {
            const loadingMessage = await this.bot.sendMessage(chatId, '🔄 **جاري إنشاء البوت...**\n\n' +
                '⏳ **التحقق من البيانات...**\n' +
                '⏳ **فحص التكرارات...**\n' +
                '⏳ **إنشاء البوت...**');

            // التحقق النهائي من جميع البيانات المطلوبة
            if (!session.data.bot_name || !session.data.server_host || !session.data.server_port || !session.data.minecraft_version) {
                await this.bot.deleteMessage(chatId, loadingMessage.message_id);
                await this.bot.sendMessage(chatId, '❌ **بيانات غير مكتملة!**\n\nيرجى البدء من جديد وإدخال جميع البيانات المطلوبة.');
                this.userSessions.delete(userId);
                return;
            }

            // التحقق النهائي من عدم تكرار الاسم للمستخدم الحالي
            const existingBotByName = await this.db.getBotByNameForUser(userId, session.data.bot_name);
            if (existingBotByName) {
                await this.bot.deleteMessage(chatId, loadingMessage.message_id);
                await this.bot.sendMessage(chatId, `❌ **اسم البوت مكرر!**\n\nلديك بوت آخر بالاسم "${session.data.bot_name}"\n\nيرجى البدء من جديد واختيار اسم مختلف.`);
                this.userSessions.delete(userId);
                return;
            }

            // التحقق النهائي من عدم تكرار السيرفر للمستخدم الحالي
            const existingBotByServer = await this.db.getBotByServerForUser(userId, session.data.server_host, session.data.server_port);
            if (existingBotByServer) {
                await this.bot.deleteMessage(chatId, loadingMessage.message_id);
                await this.bot.sendMessage(chatId, `❌ **سيرفر مكرر!**\n\nلديك بوت آخر متصل بالسيرفر "${session.data.server_host}:${session.data.server_port}"\n\nيرجى البدء من جديد واختيار سيرفر مختلف.`);
                this.userSessions.delete(userId);
                return;
            }

            const botConfig = {
                name: session.data.bot_name,
                host: session.data.server_host,
                port: session.data.server_port,
                edition: session.edition,
                version: session.data.minecraft_version
            };

            const result = await this.botManager.createBot(userId, botConfig);

            if (!result.success) {
                // حذف رسالة التحميل
                await this.bot.deleteMessage(chatId, loadingMessage.message_id);

                // إظهار رسالة الخطأ مع خيار إعادة المحاولة
                const errorMessage = `❌ **فشل في إنشاء البوت**\n\n` +
                    `🚫 **السبب:** ${result.error}\n\n` +
                    `💡 **الخيارات المتاحة:**\n` +
                    `1️⃣ إعادة المحاولة مع بيانات جديدة\n` +
                    `2️⃣ إدارة البوتات الحالية\n` +
                    `3️⃣ العودة للقائمة الرئيسية`;

                const errorKeyboard = {
                    inline_keyboard: [
                        [
                            { text: '🔄 إعادة المحاولة', callback_data: `new_bot_${session.edition}` }
                        ],
                        [
                            { text: '📋 بوتاتي', callback_data: 'my_bots' }
                        ],
                        [
                            { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                        ]
                    ]
                };

                await this.bot.sendMessage(chatId, errorMessage, {
                    parse_mode: 'Markdown',
                    reply_markup: errorKeyboard
                });

                // إزالة الجلسة بعد إرسال الرسالة
                this.userSessions.delete(userId);
                return;
            }

            const botId = result.botId;

            // حذف رسالة التحميل
            await this.bot.deleteMessage(chatId, loadingMessage.message_id);

            // إزالة الجلسة
            this.userSessions.delete(userId);

            const editionIcon = session.edition === 'java' ? '☕' : '🪨';
            const successMessage = `🎉 **تم إنشاء البوت بنجاح!**\n\n` +
                `${editionIcon} **معلومات البوت الكاملة:**\n` +
                `🔢 **الإصدار:** ${session.data.minecraft_version}\n` +
                `🌐 **السيرفر:** ${session.data.server_host}:${session.data.server_port}\n` +
                `🤖 **اسم البوت:** ${session.data.bot_name}\n` +
                `📦 **النوع:** ${session.edition === 'java' ? 'Java Edition' : 'Bedrock Edition'}\n\n` +
                `✅ **تم حفظ البوت في قاعدة البيانات**\n` +
                `🚀 **جاهز للتشغيل والاتصال بالسيرفر!**\n\n` +
                `💡 **اضغط "تشغيل البوت" لبدء الاتصال**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🚀 تشغيل البوت الآن', callback_data: `bot_action_${botId}_start` }
                    ],
                    [
                        { text: '📋 بوتاتي', callback_data: 'my_bots' }
                    ],
                    [
                        { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, successMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

            console.log(`✅ تم إنشاء بوت جديد: ${session.data.bot_name} (${session.edition} ${session.data.minecraft_version}) للمستخدم ${userId}`);

        } catch (error) {
            console.error('❌ خطأ في إنشاء البوت:', error);

            // حذف رسالة التحميل إذا كانت موجودة
            try {
                if (loadingMessage) {
                    await this.bot.deleteMessage(chatId, loadingMessage.message_id);
                }
            } catch (deleteError) {
                // تجاهل خطأ حذف الرسالة
            }

            const errorMessage = `❌ **حدث خطأ غير متوقع!**\n\n` +
                `🚫 **المشكلة:** خطأ في النظام أثناء إنشاء البوت\n\n` +
                `💡 **الحلول:**\n` +
                `1️⃣ إعادة المحاولة بعد قليل\n` +
                `2️⃣ التأكد من صحة البيانات المدخلة\n` +
                `3️⃣ التواصل مع الدعم إذا استمرت المشكلة`;

            const errorKeyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 إعادة المحاولة', callback_data: 'new_bot' }
                    ],
                    [
                        { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            await this.bot.sendMessage(chatId, errorMessage, {
                parse_mode: 'Markdown',
                reply_markup: errorKeyboard
            });

            this.userSessions.delete(userId);
        }
    }

    // إلغاء إنشاء البوت
    async cancelBotCreation(chatId, userId) {
        this.userSessions.delete(userId);

        const cancelMessage = `❌ **تم إلغاء إنشاء البوت**\n\n` +
            `🔄 **يمكنك البدء من جديد في أي وقت**`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' }
                ],
                [
                    { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, cancelMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // تغيير عنوان السيرفر أثناء إنشاء البوت
    async changeServerHost(chatId, userId) {
        const session = this.userSessions.get(userId);
        if (!session || session.type !== 'bot_creation') {
            await this.bot.sendMessage(chatId, '❌ لا توجد عملية إنشاء بوت نشطة.');
            return;
        }

        // العودة لخطوة إدخال عنوان السيرفر
        session.step = 'server_host';

        const editionIcon = session.edition === 'java' ? '☕' : '🪨';

        const message = `${editionIcon} **إنشاء البوت - ${session.edition === 'java' ? 'Java' : 'Bedrock'}**\n\n` +
            `✅ **الإصدار:** ${session.data.minecraft_version}\n\n` +
            `🌐 **الخطوة 2/5: عنوان السيرفر**\n\n` +
            `📝 **أرسل عنوان السيرفر الجديد (IP أو رابط):**\n` +
            `• مثال: play.hypixel.net\n` +
            `• مثال: *************\n` +
            `• مثال: mc.server.com\n\n` +
            `💡 **تأكد من صحة العنوان قبل الإرسال**`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '❌ إلغاء الإنشاء', callback_data: 'cancel_bot_creation' }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // معالجة إدارة البوت
    async handleBotManagement(chatId, userId, botId) {
        try {
            const botData = await this.db.getBot(botId);
            if (!botData || botData.user_id !== userId) {
                await this.bot.sendMessage(chatId, '❌ البوت غير موجود أو لا تملك صلاحية للوصول إليه');
                return;
            }

            const isActive = this.botManager.activeBots.has(botId);
            const statusIcon = isActive ? '🟢' : '🔴';
            const statusText = isActive ? 'متصل' : 'متوقف';

            const message = `🤖 **إدارة البوت**\n\n` +
                `📋 **معلومات البوت:**\n` +
                `• الاسم: ${botData.bot_name}\n` +
                `• السيرفر: ${botData.server_host}:${botData.server_port}\n` +
                `• النوع: ${botData.edition === 'java' ? '☕ Java' : '🪨 Bedrock'}\n` +
                `• الإصدار: ${botData.minecraft_version}\n` +
                `• الحالة: ${statusIcon} ${statusText}\n\n` +
                `⚙️ **الإجراءات المتاحة:**`;

            const keyboard = {
                inline_keyboard: []
            };

            if (isActive) {
                keyboard.inline_keyboard.push([
                    { text: '💬 إرسال رسالة', callback_data: `bot_action_${botId}_message` },
                    { text: '⚡ تنفيذ أمر', callback_data: `bot_action_${botId}_command` }
                ]);
                keyboard.inline_keyboard.push([
                    { text: '📊 الإحصائيات', callback_data: `bot_action_${botId}_stats` },
                    { text: '⏹️ إيقاف البوت', callback_data: `bot_action_${botId}_stop` }
                ]);
            } else {
                keyboard.inline_keyboard.push([
                    { text: '▶️ تشغيل البوت', callback_data: `bot_action_${botId}_start` }
                ]);
                keyboard.inline_keyboard.push([
                    { text: '🔧 تعديل البوت', callback_data: `bot_action_${botId}_edit` },
                    { text: '🗑️ حذف البوت', callback_data: `bot_action_${botId}_delete` }
                ]);
            }

            keyboard.inline_keyboard.push([
                { text: '🔙 بوتاتي', callback_data: 'my_bots' }
            ]);

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في إدارة البوت:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في إدارة البوت');
        }
    }

    // معالجة إجراءات البوت
    async handleBotAction(chatId, userId, botId, action) {
        try {
            const botData = await this.db.getBot(botId);
            if (!botData || botData.user_id !== userId) {
                await this.bot.sendMessage(chatId, '❌ البوت غير موجود أو لا تملك صلاحية للوصول إليه');
                return;
            }

            switch (action) {
                case 'start':
                    await this.handleStartBot(chatId, botId, botData);
                    break;
                case 'stop':
                    await this.handleStopBot(chatId, botId, botData);
                    break;
                case 'delete':
                    await this.handleDeleteBot(chatId, botId, botData);
                    break;
                case 'stats':
                    await this.handleBotStats(chatId, botId, botData);
                    break;
                default:
                    await this.bot.sendMessage(chatId, '❌ إجراء غير معروف');
            }

        } catch (error) {
            console.error('❌ خطأ في تنفيذ إجراء البوت:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في تنفيذ الإجراء');
        }
    }

    // تشغيل البوت
    async handleStartBot(chatId, botId, botData) {
        const loadingMessage = await this.bot.sendMessage(chatId, '🔄 **جاري تشغيل البوت...**\n\nيرجى الانتظار...');

        try {
            const result = await this.botManager.startBot(botId);

            await this.bot.deleteMessage(chatId, loadingMessage.message_id);

            if (result.success) {
                const editionIcon = botData.edition === 'java' ? '☕' : '🪨';
                const connectingMessage = `🔄 **جاري الاتصال بالسيرفر...**\n\n` +
                    `${editionIcon} **البوت:** ${botData.bot_name}\n` +
                    `🌐 **السيرفر:** ${botData.server_host}:${botData.server_port}\n` +
                    `📦 **الإصدار:** ${botData.minecraft_version}\n\n` +
                    `⏳ **يرجى الانتظار...**\n` +
                    `سيتم إشعارك عند نجاح الاتصال أو في حالة وجود مشكلة`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '⏹️ إيقاف البوت', callback_data: `bot_action_${botId}_stop` }
                        ],
                        [
                            { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, connectingMessage, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
                console.log(`🚀 تم بدء تشغيل البوت ${botData.bot_name} للمستخدم ${botData.user_id}`);
            } else {
                const errorMessage = `❌ **فشل في تشغيل البوت**\n\n` +
                    `🤖 **البوت:** ${botData.bot_name}\n` +
                    `❌ **السبب:** ${result.error}\n\n` +
                    `💡 **الحلول المقترحة:**\n` +
                    `• تحقق من عنوان السيرفر والمنفذ\n` +
                    `• تأكد من أن السيرفر يعمل\n` +
                    `• جرب إعادة التشغيل مرة أخرى`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '🔄 إعادة المحاولة', callback_data: `bot_action_${botId}_start` }
                        ],
                        [
                            { text: '🔧 تعديل البوت', callback_data: `bot_action_${botId}_edit` },
                            { text: '🗑️ حذف البوت', callback_data: `bot_action_${botId}_delete` }
                        ],
                        [
                            { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                        ]
                    ]
                };

                await this.bot.sendMessage(chatId, errorMessage, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });
            }

        } catch (error) {
            await this.bot.deleteMessage(chatId, loadingMessage.message_id);

            const errorMessage = `💥 **خطأ في تشغيل البوت**\n\n` +
                `🤖 **البوت:** ${botData.bot_name}\n` +
                `❌ **الخطأ:** ${error.message}\n\n` +
                `🔧 **يرجى المحاولة مرة أخرى أو التواصل مع الدعم**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 إعادة المحاولة', callback_data: `bot_action_${botId}_start` }
                    ],
                    [
                        { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                    ]
                ]
            };

            await this.bot.sendMessage(chatId, errorMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });
        }
    }

    // إيقاف البوت
    async handleStopBot(chatId, botId, botData) {
        try {
            const result = await this.botManager.stopBot(botId);

            if (result.success) {
                const message = `⏹️ **تم إيقاف البوت**\n\n` +
                    `🤖 **البوت:** ${botData.bot_name}\n` +
                    `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}`;

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '▶️ تشغيل مرة أخرى', callback_data: `bot_action_${botId}_start` }
                        ],
                        [
                            { text: '🔙 بوتاتي', callback_data: 'my_bots' }
                        ]
                    ]
                };

                const sentMessage = await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });

                await this.setLastActionMessage(chatId, sentMessage.message_id);
            } else {
                await this.bot.sendMessage(chatId, `❌ فشل في إيقاف البوت: ${result.error}`);
            }

        } catch (error) {
            await this.bot.sendMessage(chatId, `❌ خطأ في إيقاف البوت: ${error.message}`);
        }
    }

    // حذف البوت
    async handleDeleteBot(chatId, botId, botData) {
        const confirmMessage = `🗑️ **تأكيد حذف البوت**\n\n` +
            `🤖 **البوت:** ${botData.bot_name}\n` +
            `🌐 **السيرفر:** ${botData.server_host}:${botData.server_port}\n\n` +
            `⚠️ **تحذير:** هذا الإجراء لا يمكن التراجع عنه!`;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '✅ نعم، احذف البوت', callback_data: `confirm_delete_${botId}` }
                ],
                [
                    { text: '❌ إلغاء', callback_data: `bot_manage_${botId}` }
                ]
            ]
        };

        const sentMessage = await this.bot.sendMessage(chatId, confirmMessage, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });

        await this.setLastActionMessage(chatId, sentMessage.message_id);
    }

    // إحصائيات البوت
    async handleBotStats(chatId, botId, botData) {
        try {
            const stats = this.botManager.getBotStats(botId);
            const isActive = this.botManager.activeBots.has(botId);

            const message = `📊 **إحصائيات البوت**\n\n` +
                `🤖 **البوت:** ${botData.bot_name}\n` +
                `🌐 **السيرفر:** ${botData.server_host}:${botData.server_port}\n` +
                `📦 **النوع:** ${botData.edition === 'java' ? '☕ Java' : '🪨 Bedrock'}\n` +
                `🔢 **الإصدار:** ${botData.minecraft_version}\n\n` +
                `📈 **الإحصائيات:**\n` +
                `• الحالة: ${isActive ? '🟢 متصل' : '🔴 متوقف'}\n` +
                `• عدد الاتصالات: ${botData.connection_count || 0}\n` +
                `• آخر اتصال: ${botData.last_connected ? new Date(botData.last_connected).toLocaleString('ar-EG') : 'لم يتصل بعد'}\n` +
                `• تاريخ الإنشاء: ${new Date(botData.created_at).toLocaleString('ar-EG')}`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🔄 تحديث', callback_data: `bot_action_${botId}_stats` }
                    ],
                    [
                        { text: '🔙 إدارة البوت', callback_data: `bot_manage_${botId}` }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, message, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);

        } catch (error) {
            console.error('❌ خطأ في عرض إحصائيات البوت:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في عرض الإحصائيات');
        }
    }

    // تأكيد حذف البوت
    async confirmDeleteBot(chatId, userId, botId) {
        try {
            const botData = await this.db.getBot(botId);
            if (!botData || botData.user_id !== userId) {
                await this.bot.sendMessage(chatId, '❌ البوت غير موجود أو لا تملك صلاحية للوصول إليه');
                return;
            }

            // إيقاف البوت إذا كان يعمل
            if (this.botManager.activeBots.has(botId)) {
                await this.botManager.stopBot(botId);
            }

            // حذف البوت من قاعدة البيانات
            await this.db.deleteBot(botId);

            const deleteMessage = `🗑️ **تم حذف البوت بنجاح**\n\n` +
                `🤖 **البوت المحذوف:** ${botData.bot_name}\n` +
                `🌐 **السيرفر:** ${botData.server_host}:${botData.server_port}\n` +
                `⏰ **وقت الحذف:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `✅ **تم حذف جميع البيانات المرتبطة بالبوت**`;

            const keyboard = {
                inline_keyboard: [
                    [
                        { text: '🤖 إنشاء بوت جديد', callback_data: 'new_bot' }
                    ],
                    [
                        { text: '📋 بوتاتي', callback_data: 'my_bots' },
                        { text: '🏠 القائمة الرئيسية', callback_data: 'main_menu' }
                    ]
                ]
            };

            const sentMessage = await this.bot.sendMessage(chatId, deleteMessage, {
                parse_mode: 'Markdown',
                reply_markup: keyboard
            });

            await this.setLastActionMessage(chatId, sentMessage.message_id);
            console.log(`🗑️ تم حذف البوت ${botData.bot_name} للمستخدم ${userId}`);

        } catch (error) {
            console.error('❌ خطأ في حذف البوت:', error);
            await this.bot.sendMessage(chatId, '❌ حدث خطأ في حذف البوت');
        }
    }

    // معالجة إعادة تشغيل البوت التلقائي بعد استعادة النسخة الاحتياطية
    async handleBotAutoRestarted(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const message = `✅ **تم إعادة تشغيل البوت تلقائياً**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🌐 **السيرفر:** ${data.serverHost}:${data.serverPort}\n` +
                `🔄 **السبب:** استعادة النسخة الاحتياطية\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `🎮 **البوت جاهز للاستخدام الآن!**`;

            await this.bot.sendMessage(user.telegram_id, message, {
                parse_mode: 'Markdown',
                reply_markup: {
                    inline_keyboard: [
                        [
                            { text: '📊 عرض حالة البوت', callback_data: `bot_status_${data.botId}` },
                            { text: '🎮 إدارة البوتات', callback_data: 'manage_bots' }
                        ]
                    ]
                }
            });

        } catch (error) {
            console.error('❌ خطأ في معالجة إعادة التشغيل التلقائي:', error);
        }
    }

    // معالجة فشل إعادة تشغيل البوت التلقائي
    async handleBotAutoRestartFailed(data) {
        try {
            const user = await this.db.getUser(data.userId);
            if (!user) return;

            const message = `❌ **فشل في إعادة تشغيل البوت تلقائياً**\n\n` +
                `🤖 **البوت:** ${data.botName}\n` +
                `🔄 **السبب:** استعادة النسخة الاحتياطية\n` +
                `❌ **خطأ:** ${data.error}\n` +
                `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}\n\n` +
                `💡 **يمكنك إعادة تشغيل البوت يدوياً**`;

            await this.bot.sendMessage(user.telegram_id, message, {
                parse_mode: 'Markdown',
                reply_markup: {
                    inline_keyboard: [
                        [
                            { text: '🚀 تشغيل البوت', callback_data: `start_bot_${data.botId}` },
                            { text: '⚙️ تعديل البوت', callback_data: `edit_bot_${data.botId}` }
                        ],
                        [
                            { text: '🎮 إدارة البوتات', callback_data: 'manage_bots' }
                        ]
                    ]
                }
            });

        } catch (error) {
            console.error('❌ خطأ في معالجة فشل إعادة التشغيل التلقائي:', error);
        }
    }

    // معالجة اكتمال استعادة البوتات
    async handleBotsRestoreCompleted(data) {
        try {
            // إرسال تقرير للمدراء
            const adminIds = process.env.ADMIN_IDS ?
                process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())) :
                [];

            if (adminIds.length > 0) {
                const message = `📊 **تقرير استعادة البوتات**\n\n` +
                    `🔄 **تم اكتمال استعادة البوتات بعد استعادة النسخة الاحتياطية**\n\n` +
                    `📈 **الإحصائيات:**\n` +
                    `• إجمالي البوتات: ${data.totalBots}\n` +
                    `• نجح: ${data.successCount} ✅\n` +
                    `• فشل: ${data.failCount} ❌\n` +
                    `• معدل النجاح: ${Math.round((data.successCount / data.totalBots) * 100)}%\n\n` +
                    `⏰ **الوقت:** ${new Date().toLocaleString('ar-EG')}`;

                for (const adminId of adminIds) {
                    try {
                        await this.bot.sendMessage(adminId, message, {
                            parse_mode: 'Markdown'
                        });
                    } catch (error) {
                        console.error(`❌ خطأ في إرسال تقرير استعادة البوتات للمدير ${adminId}:`, error);
                    }
                }
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة اكتمال استعادة البوتات:', error);
        }
    }

    // إغلاق بوت التلغرام
    async close() {
        try {
            console.log('📱 إغلاق بوت التلغرام...');

            if (this.bot) {
                await this.bot.stopPolling();
            }

            if (this.botManager) {
                await this.botManager.close();
            }

            if (this.db) {
                await this.db.close();
            }

            console.log('✅ تم إغلاق بوت التلغرام بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إغلاق بوت التلغرام:', error);
        }
    }
}

module.exports = TelegramBotManager;
