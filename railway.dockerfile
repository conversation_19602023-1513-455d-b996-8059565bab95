# Dockerfile خاص بـ Railway لحل مشكلة raknet-native
FROM node:18

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHON=/usr/bin/python3

# تحديث النظام وتثبيت الأدوات المطلوبة
RUN apt-get update && apt-get install -y \
    curl \
    python3 \
    python3-pip \
    build-essential \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مجلد العمل
WORKDIR /app

# نسخ ملفات package أولاً للاستفادة من Docker cache
COPY package*.json ./

# التحقق من وجود package-lock.json وتثبيت التبعيات
RUN if [ -f package-lock.json ]; then \
      echo "📦 استخدام npm ci مع package-lock.json"; \
      npm ci --omit=dev --verbose; \
    else \
      echo "📦 استخدام npm install بدون package-lock.json"; \
      npm install --omit=dev --verbose; \
    fi && \
    npm cache clean --force

# نسخ باقي الملفات
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p logs backups temp data uploads

# تعيين الصلاحيات
RUN chmod +x start.sh 2>/dev/null || echo "start.sh not found, skipping chmod"

# كشف المنفذ (Railway سيحدد المنفذ تلقائياً)
EXPOSE 3001

# فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# تشغيل التطبيق
CMD ["npm", "start"]
