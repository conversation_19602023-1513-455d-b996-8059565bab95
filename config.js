/**
 * 🎮 إعدادات النظام المتقدم
 * نظام إعدادات شامل ومرن لبوت ماينكرافت المتقدم
 */

const path = require('path');
const fs = require('fs');

class ConfigManager {
    constructor() {
        this.config = this.loadConfig();
        this.validateConfig();
    }

    loadConfig() {
        return {
            // 📱 إعدادات بوت التلغرام
            telegram: {
                token: process.env.TELEGRAM_BOT_TOKEN,
                polling: {
                    interval: 1000,
                    timeout: 10,
                    autoStart: true
                },
                messages: {
                    maxLength: 4096,
                    parseMode: 'Markdown',
                    disableWebPagePreview: true
                },
                security: {
                    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
                    maxRequestsPerWindow: parseInt(process.env.MAX_REQUESTS_PER_WINDOW) || 30,
                    adminOnly: process.env.ADMIN_ONLY === 'true' || false
                }
            },

            // 🤖 إعدادات البوتات
            bots: {
                maxBotsPerUser: parseInt(process.env.MAX_BOTS_PER_USER) || 1, // بوت واحد فقط لكل مستخدم
                reconnection: {
                    maxAttempts: parseInt(process.env.MAX_RECONNECT_ATTEMPTS) || 30, // 5 دقائق × 6 محاولات/دقيقة
                    delay: parseInt(process.env.RECONNECT_DELAY) || 10000, // 10 ثواني
                    backoffMultiplier: parseFloat(process.env.BACKOFF_MULTIPLIER) || 1.0, // بدون زيادة
                    maxDelay: parseInt(process.env.MAX_RECONNECT_DELAY) || 10000 // 10 ثواني ثابت
                },
                timeout: {
                    connection: parseInt(process.env.CONNECTION_TIMEOUT) || 30000,
                    keepAlive: parseInt(process.env.KEEP_ALIVE_TIMEOUT) || 30000,
                    response: parseInt(process.env.RESPONSE_TIMEOUT) || 10000
                },
                monitoring: {
                    enabled: process.env.MONITORING_ENABLED !== 'false',
                    interval: parseInt(process.env.MONITORING_INTERVAL) || 30000,
                    healthCheck: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 60000
                }
            },

            // 🎯 الإصدارات المدعومة (محدثة لأحدث الإصدارات)
            supportedVersions: {
                java: [
                    '1.21.4',    // أحدث إصدار
                    '1.21.3',
                    '1.21.1',
                    '1.21.0',
                    '1.20.6',
                    '1.20.4',
                    '1.20.2',
                    '1.20.1'
                ],
                bedrock: [
                    '1.21.93',   // أحدث إصدار
                    '1.21.92',
                    '1.21.90',
                    '1.21.80',
                    '1.21.70',
                    '1.20.80',
                    '1.20.30',   // محدث
                    '1.20.15'
                ]
            },

            // 🗄️ إعدادات قاعدة البيانات
            database: {
                url: process.env.DATABASE_URL || 'sqlite:./minecraft_bot.db',
                options: {
                    pool: {
                        min: parseInt(process.env.DB_POOL_MIN) || 2,
                        max: parseInt(process.env.DB_POOL_MAX) || 10
                    },
                    timeout: parseInt(process.env.DB_TIMEOUT) || 30000,
                    retries: parseInt(process.env.DB_RETRIES) || 3
                },
                backup: {
                    enabled: process.env.BACKUP_ENABLED !== 'false',
                    autoBackup: process.env.AUTO_BACKUP_ENABLED !== 'false',
                    interval: parseInt(process.env.AUTO_BACKUP_INTERVAL) || 300000, // 5 دقائق
                    maxBackups: parseInt(process.env.MAX_BACKUPS) || 50,
                    compression: process.env.BACKUP_COMPRESSION !== 'false',
                    encryption: process.env.BACKUP_ENCRYPTION === 'true'
                }
            },

            // 🔒 إعدادات الأمان
            security: {
                encryption: {
                    enabled: process.env.ENCRYPTION_ENABLED === 'true',
                    algorithm: 'aes-256-gcm',
                    keyLength: 32
                },
                protection: {
                    maxFailedAttempts: parseInt(process.env.MAX_FAILED_ATTEMPTS) || 5,
                    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 900000, // 15 دقيقة
                    ipWhitelist: process.env.IP_WHITELIST ? process.env.IP_WHITELIST.split(',') : [],
                    ipBlacklist: process.env.IP_BLACKLIST ? process.env.IP_BLACKLIST.split(',') : []
                },
                sessions: {
                    duration: parseInt(process.env.SESSION_DURATION) || 86400000, // 24 ساعة
                    cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL) || 3600000 // ساعة
                }
            },

            // 📊 إعدادات المراقبة والتنبيهات
            monitoring: {
                performance: {
                    enabled: process.env.PERFORMANCE_MONITORING !== 'false',
                    interval: parseInt(process.env.MONITORING_INTERVAL) || 60000,
                    metrics: ['cpu', 'memory', 'connections', 'errors', 'uptime']
                },
                alerts: {
                    enabled: process.env.ALERTS_ENABLED !== 'false',
                    thresholds: {
                        memoryUsage: parseInt(process.env.MEMORY_THRESHOLD) || 80,
                        cpuUsage: parseInt(process.env.CPU_THRESHOLD) || 80,
                        errorRate: parseInt(process.env.ERROR_RATE_THRESHOLD) || 10,
                        connectionFailures: parseInt(process.env.CONNECTION_FAILURE_THRESHOLD) || 5
                    },
                    notifications: {
                        telegram: process.env.ALERT_TELEGRAM_ENABLED !== 'false',
                        console: process.env.ALERT_CONSOLE_ENABLED !== 'false'
                    }
                }
            },

            // 📝 إعدادات السجلات
            logging: {
                level: process.env.LOG_LEVEL || 'info',
                console: {
                    enabled: process.env.CONSOLE_LOGGING !== 'false',
                    colors: process.env.LOG_COLORS !== 'false',
                    timestamp: process.env.LOG_TIMESTAMP !== 'false'
                },
                file: {
                    enabled: process.env.FILE_LOGGING === 'true',
                    path: process.env.LOG_PATH || './logs/',
                    maxSize: parseInt(process.env.MAX_LOG_SIZE) || 10485760, // 10MB
                    maxFiles: parseInt(process.env.MAX_LOG_FILES) || 5,
                    datePattern: 'YYYY-MM-DD'
                }
            },

            // 🌐 إعدادات الشبكة
            network: {
                proxy: {
                    enabled: process.env.PROXY_ENABLED === 'true',
                    host: process.env.PROXY_HOST || '',
                    port: parseInt(process.env.PROXY_PORT) || 0,
                    username: process.env.PROXY_USERNAME || '',
                    password: process.env.PROXY_PASSWORD || ''
                },
                dns: {
                    timeout: parseInt(process.env.DNS_TIMEOUT) || 5000,
                    retries: parseInt(process.env.DNS_RETRIES) || 3,
                    servers: process.env.DNS_SERVERS ? 
                        process.env.DNS_SERVERS.split(',') : 
                        ['*******', '*******', '**************']
                }
            },

            // 🎮 إعدادات الألعاب
            game: {
                defaultSettings: {
                    autoRespawn: process.env.AUTO_RESPAWN !== 'false',
                    autoReconnect: process.env.AUTO_RECONNECT !== 'false',
                    chatLogging: process.env.CHAT_LOGGING !== 'false',
                    commandLogging: process.env.COMMAND_LOGGING !== 'false'
                },
                limits: {
                    maxMessageLength: parseInt(process.env.MAX_MESSAGE_LENGTH) || 256,
                    maxCommandLength: parseInt(process.env.MAX_COMMAND_LENGTH) || 100,
                    commandCooldown: parseInt(process.env.COMMAND_COOLDOWN) || 1000
                }
            },

            // 🚀 إعدادات الأداء
            performance: {
                maxMemoryUsage: parseInt(process.env.MAX_MEMORY_USAGE) || 1073741824, // 1GB
                gcInterval: parseInt(process.env.GC_INTERVAL) || 300000, // 5 دقائق
                statsUpdateInterval: parseInt(process.env.STATS_UPDATE_INTERVAL) || 60000,
                caching: {
                    enabled: process.env.CACHING_ENABLED !== 'false',
                    ttl: parseInt(process.env.CACHE_TTL) || 300000, // 5 دقائق
                    maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 1000
                }
            },

            // 🔧 إعدادات التطوير
            development: {
                debug: process.env.DEBUG_MODE === 'true' || process.env.NODE_ENV === 'development',
                hotReload: process.env.HOT_RELOAD === 'true',
                testMode: process.env.TEST_MODE === 'true',
                simulateErrors: process.env.SIMULATE_ERRORS === 'true',
                verboseLogging: process.env.VERBOSE_LOGGING === 'true'
            },

            // 🌍 إعدادات التدويل
            localization: {
                defaultLanguage: process.env.DEFAULT_LANGUAGE || 'ar',
                supportedLanguages: ['ar', 'en'],
                autoDetect: process.env.AUTO_DETECT_LANGUAGE !== 'false'
            },

            // 📊 إعدادات الإحصائيات
            statistics: {
                enabled: process.env.STATISTICS_ENABLED !== 'false',
                detailed: process.env.DETAILED_STATISTICS === 'true',
                retention: parseInt(process.env.STATS_RETENTION) || 2592000000, // 30 يوم
                aggregation: {
                    enabled: process.env.STATS_AGGREGATION !== 'false',
                    interval: parseInt(process.env.STATS_AGGREGATION_INTERVAL) || 3600000 // ساعة
                }
            }
        };
    }

    // التحقق من صحة الإعدادات
    validateConfig() {
        const errors = [];

        // التحقق من التوكن
        if (!this.config.telegram.token || this.config.telegram.token === 'YOUR_BOT_TOKEN_HERE') {
            errors.push('❌ توكن بوت التلغرام غير صحيح أو غير موجود');
        }

        // التحقق من الإصدارات المدعومة
        if (!this.config.supportedVersions.java.length || !this.config.supportedVersions.bedrock.length) {
            errors.push('❌ قائمة الإصدارات المدعومة فارغة');
        }

        // التحقق من إعدادات قاعدة البيانات
        if (!this.config.database.url) {
            errors.push('❌ رابط قاعدة البيانات غير محدد');
        }

        if (errors.length > 0) {
            console.error('🚨 أخطاء في الإعدادات:');
            errors.forEach(error => console.error(error));
            if (!this.config.development.debug) {
                process.exit(1);
            }
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // الحصول على إعداد معين
    get(path) {
        return path.split('.').reduce((obj, key) => obj && obj[key], this.config);
    }

    // تحديث إعداد معين
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, this.config);
        target[lastKey] = value;
    }

    // الحصول على جميع الإعدادات
    getAll() {
        return this.config;
    }

    // إعادة تحميل الإعدادات
    reload() {
        this.config = this.loadConfig();
        this.validateConfig();
        return this.config;
    }
}

// إنشاء مثيل واحد للاستخدام العام
const configManager = new ConfigManager();

module.exports = {
    config: configManager.getAll(),
    get: configManager.get.bind(configManager),
    set: configManager.set.bind(configManager),
    reload: configManager.reload.bind(configManager),
    validate: configManager.validateConfig.bind(configManager)
};
