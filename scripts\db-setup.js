#!/usr/bin/env node

/**
 * 🔧 إعداد قاعدة البيانات الأولي
 * يقوم بإنشاء قاعدة البيانات وتشغيل الترقيات وإدراج البيانات الافتراضية
 */

const DatabaseManager = require('../database');
const MigrationManager = require('./migrate');

class DatabaseSetup {
    constructor() {
        this.dbManager = null;
        this.migrationManager = null;
    }

    async setup() {
        try {
            console.log('🔧 بدء إعداد قاعدة البيانات...');

            // تهيئة مدير قاعدة البيانات
            this.dbManager = new DatabaseManager();
            await this.dbManager.init();

            // تشغيل الترقيات
            this.migrationManager = new MigrationManager();
            await this.migrationManager.init();
            await this.migrationManager.migrate('up');

            // إدراج البيانات الافتراضية
            await this.insertDefaultData();

            console.log('✅ تم إعداد قاعدة البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    async insertDefaultData() {
        try {
            console.log('📝 إدراج البيانات الافتراضية...');

            // إعدادات النظام الافتراضية
            const defaultSettings = [
                {
                    key: 'max_bots_per_user',
                    value: '5',
                    value_type: 'number',
                    category: 'limits',
                    description: 'الحد الأقصى لعدد البوتات لكل مستخدم'
                },
                {
                    key: 'max_reconnect_attempts',
                    value: '15',
                    value_type: 'number',
                    category: 'connection',
                    description: 'الحد الأقصى لمحاولات إعادة الاتصال'
                },
                {
                    key: 'reconnect_delay',
                    value: '20000',
                    value_type: 'number',
                    category: 'connection',
                    description: 'تأخير إعادة الاتصال بالميلي ثانية'
                },
                {
                    key: 'auto_backup_enabled',
                    value: 'true',
                    value_type: 'boolean',
                    category: 'backup',
                    description: 'تفعيل النسخ الاحتياطي التلقائي'
                },
                {
                    key: 'auto_backup_interval',
                    value: '300000',
                    value_type: 'number',
                    category: 'backup',
                    description: 'فترة النسخ الاحتياطي التلقائي بالميلي ثانية'
                },
                {
                    key: 'monitoring_enabled',
                    value: 'true',
                    value_type: 'boolean',
                    category: 'monitoring',
                    description: 'تفعيل نظام المراقبة'
                },
                {
                    key: 'alerts_enabled',
                    value: 'true',
                    value_type: 'boolean',
                    category: 'alerts',
                    description: 'تفعيل نظام التنبيهات'
                },
                {
                    key: 'default_language',
                    value: 'ar',
                    value_type: 'string',
                    category: 'localization',
                    description: 'اللغة الافتراضية للنظام'
                },
                {
                    key: 'session_duration',
                    value: '86400000',
                    value_type: 'number',
                    category: 'security',
                    description: 'مدة الجلسة بالميلي ثانية'
                },
                {
                    key: 'rate_limit_window',
                    value: '60000',
                    value_type: 'number',
                    category: 'security',
                    description: 'نافزة تحديد المعدل بالميلي ثانية'
                }
            ];

            // إدراج الإعدادات
            for (const setting of defaultSettings) {
                try {
                    await this.dbManager.setSetting(
                        setting.key,
                        setting.value,
                        setting.value_type,
                        setting.category,
                        setting.description
                    );
                    console.log(`✅ تم إدراج الإعداد: ${setting.key}`);
                } catch (error) {
                    if (error.message && error.message.includes('UNIQUE constraint failed')) {
                        console.log(`⚠️ الإعداد موجود بالفعل: ${setting.key}`);
                    } else {
                        console.error(`❌ خطأ في إدراج الإعداد ${setting.key}:`, error);
                    }
                }
            }

            console.log('✅ تم إدراج البيانات الافتراضية بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إدراج البيانات الافتراضية:', error);
            throw error;
        }
    }

    async cleanup() {
        try {
            if (this.migrationManager) {
                await this.migrationManager.close();
            }
            if (this.dbManager) {
                await this.dbManager.close();
            }
        } catch (error) {
            console.error('❌ خطأ في تنظيف الموارد:', error);
        }
    }

    // فحص حالة قاعدة البيانات
    async checkStatus() {
        try {
            console.log('🔍 فحص حالة قاعدة البيانات...');

            this.dbManager = new DatabaseManager();
            await this.dbManager.init();

            // فحص الجداول
            const tables = ['users', 'bots', 'bot_stats', 'settings', 'backups'];
            console.log('\n📋 حالة الجداول:');
            
            for (const table of tables) {
                try {
                    const count = await this.getTableCount(table);
                    console.log(`  ✅ ${table}: ${count} سجل`);
                } catch (error) {
                    console.log(`  ❌ ${table}: غير موجود أو خطأ`);
                }
            }

            // فحص الإعدادات
            console.log('\n⚙️ الإعدادات المحفوظة:');
            try {
                const settings = await this.dbManager.getAllSettings();
                settings.forEach(setting => {
                    console.log(`  • ${setting.key}: ${setting.value} (${setting.value_type})`);
                });
            } catch (error) {
                console.log('  ❌ خطأ في قراءة الإعدادات');
            }

            console.log('\n✅ تم فحص حالة قاعدة البيانات');

        } catch (error) {
            console.error('❌ خطأ في فحص حالة قاعدة البيانات:', error);
        } finally {
            await this.cleanup();
        }
    }

    async getTableCount(tableName) {
        if (this.dbManager.dbType === 'postgresql') {
            const client = await this.dbManager.client.connect();
            try {
                const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                return result.rows[0].count;
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                this.dbManager.db.get(`SELECT COUNT(*) as count FROM ${tableName}`, (err, row) => {
                    if (err) reject(err);
                    else resolve(row.count);
                });
            });
        }
    }
}

// تشغيل النظام
async function main() {
    const command = process.argv[2] || 'setup';
    const dbSetup = new DatabaseSetup();

    try {
        switch (command) {
            case 'setup':
                await dbSetup.setup();
                break;
            case 'status':
                await dbSetup.checkStatus();
                break;
            default:
                console.log('الأوامر المتاحة:');
                console.log('  setup  - إعداد قاعدة البيانات');
                console.log('  status - فحص حالة قاعدة البيانات');
        }
    } catch (error) {
        console.error('❌ فشل في تنفيذ العملية:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = DatabaseSetup;
