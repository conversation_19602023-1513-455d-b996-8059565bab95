/**
 * ☕ بوت ماينكرافت Java Edition المحسن
 * دعم كامل لجميع ميزات Java Edition مع إعادة اتصال ذكية
 */

const mineflayer = require('mineflayer');
const { EventEmitter } = require('events');
const { config } = require('./config');

class MinecraftJavaBot extends EventEmitter {
    constructor(botConfig) {
        super();
        this.config = botConfig;
        this.bot = null;
        this.isConnected = false;
        this.connectionTime = null;
        this.disconnectionTime = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 30; // 5 دقائق × 6 محاولات/دقيقة
        this.reconnectDelay = config.bots.reconnection.delay;
        this.shouldReconnect = true;
        this.reconnectTimeout = null;
        this.isConnecting = false; // منع المحاولات المتعددة
        this.playerInfo = {};
        this.stats = {
            messagesSent: 0,
            commandsExecuted: 0,
            deaths: 0,
            respawns: 0,
            errors: 0
        };
        
        console.log(`🔧 تم إنشاء بوت Java: ${this.config.username}`);
    }

    // الاتصال بالسيرفر
    async connect() {
        // منع المحاولات المتعددة
        if (this.isConnecting) {
            console.log(`⚠️ محاولة اتصال جارية بالفعل للبوت ${this.config.username}`);
            return;
        }

        this.isConnecting = true;

        try {
            console.log(`🔄 محاولة الاتصال بسيرفر Java: ${this.config.host}:${this.config.port}`);
            console.log(`📦 إصدار البوت: ${this.config.version}`);
            
            const botOptions = {
                host: this.config.host,
                port: this.config.port,
                username: this.config.username,
                version: this.config.version,
                auth: 'offline', // وضع offline للسيرفرات المحلية
                hideErrors: false,
                skipValidation: true,
                checkTimeoutInterval: 30000,
                keepAlive: true,
                connectTimeout: config.bots.timeout.connection,
                onMsaCode: (data) => {
                    console.log(`🔐 كود المصادقة: ${data.user_code}`);
                    console.log(`🌐 اذهب إلى: ${data.verification_uri}`);
                }
            };

            // إضافة مصادقة Microsoft إذا كانت متطلبة
            if (this.config.microsoftAuth) {
                botOptions.auth = 'microsoft';
            }

            // استخدام الإصدار الذي اختاره المستخدم دائماً
            botOptions.version = this.config.version;
            console.log(`🎯 استخدام إصدار البوت المحدد: ${this.config.version}`);

            this.bot = mineflayer.createBot(botOptions);
            this.setupEventHandlers();

            // إضافة معالج للأخطاء الأولية
            this.bot.on('error', (error) => {
                this.isConnecting = false; // إنهاء حالة الاتصال
                if (!this.isConnected) {
                    console.log(`❌ فشل الاتصال الأولي: ${error.message}`);
                    // بدء إعادة المحاولة إذا كان السبب يشير لسيرفر مطفي
                    if (this.isServerDownReason(error.message)) {
                        this.handleReconnect();
                    }
                }
            });

        } catch (error) {
            this.isConnecting = false; // إنهاء حالة الاتصال
            console.error(`❌ خطأ في إنشاء بوت Java: ${error.message}`);
            // بدء إعادة المحاولة إذا كان السبب يشير لسيرفر مطفي
            if (this.isServerDownReason(error.message)) {
                this.handleReconnect();
            } else {
                this.emit('error', error);
            }
        }
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // عند الاتصال بنجاح - نرسل connected مع isRealConnection: false مثل البيدروك
        this.bot.on('login', () => {
            console.log(`✅ تم الاتصال بنجاح بسيرفر Java ${this.config.host}:${this.config.port}`);
            this.isConnecting = false; // إنهاء حالة الاتصال
            this.connectionTime = new Date();
            this.reconnectAttempts = 0;

            // تنظيف timeout إعادة الاتصال إذا كان موجود
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = null;
            }

            // إرسال connected مع isRealConnection: false مثل البيدروك
            this.emit('connected', {
                connectionTime: this.connectionTime,
                isRealConnection: false, // مثل البيدروك - ليس اتصال حقيقي بعد
                serverInfo: {
                    host: this.config.host,
                    port: this.config.port,
                    version: this.config.version
                }
            });
        });

        // عند دخول العالم - مثل بوت البيدروك تماماً
        this.bot.on('spawn', () => {
            console.log(`🌍 دخل البوت ${this.config.username} إلى عالم Java`);

            // الآن البوت متصل فعلياً ودخل العالم
            this.isConnected = true;
            this.playerInfo = {
                entityId: this.bot.entity?.id,
                position: this.bot.entity?.position,
                dimension: this.bot.game?.dimension
            };

            // إرسال حدث الاتصال أولاً
            this.emit('connected', {
                connectionTime: this.connectionTime,
                isRealConnection: true, // تأكيد أن هذا اتصال حقيقي
                serverInfo: {
                    host: this.config.host,
                    port: this.config.port,
                    version: this.config.version
                }
            });

            // ثم إرسال حدث بدء اللعبة
            this.emit('gameStarted', {
                playerInfo: this.playerInfo,
                gameMode: this.bot.game?.gameMode,
                dimension: this.bot.game?.dimension
            });

            console.log(`✅ البوت ${this.config.username} متصل بنجاح`);

            // وأخيراً إرسال حدث دخول العالم - مثل البيدروك تماماً
            console.log(`🌍 دخل البوت ${this.config.username} إلى عالم Java`);
            this.emit('worldJoined', {
                username: this.config.username,
                host: this.config.host,
                port: this.config.port
            });

            console.log(`🌍 البوت ${this.config.username} دخل إلى العالم بنجاح`);
        });

        // عند استقبال رسالة في الشات
        this.bot.on('chat', (username, message) => {
            if (username !== this.config.username && username !== '!') {
                console.log(`💬 [${username}]: ${message}`);
                this.emit('chatMessage', {
                    username: username,
                    message: message,
                    timestamp: new Date()
                });
            }
        });

        // عند انقطاع الاتصال
        this.bot.on('end', (reason) => {
            console.log(`🔌 السيرفر ${this.config.host}:${this.config.port} مطفي أو غير متصل`);

            // إذا انقطع الاتصال بسرعة (أقل من 3 ثواني)، فهذا يعني أن السيرفر مطفي
            const connectionDuration = this.connectionTime ? Date.now() - this.connectionTime.getTime() : 0;
            const wasRealConnection = connectionDuration > 3000; // 3 ثواني

            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال
            this.disconnectionTime = new Date();

            // إرسال حدث الانقطاع مع معلومات مفصلة
            this.emit('disconnected', {
                connectionTime: this.connectionTime,
                disconnectionTime: this.disconnectionTime,
                duration: this.getSessionDuration(),
                wasRealConnection: wasRealConnection, // إرسال معلومة الاتصال الحقيقي
                connectionDuration: connectionDuration // إرسال مدة الاتصال
            });

            if (!wasRealConnection) {
                console.log(`⚠️ تجاهل انقطاع سريع للبوت ${this.config.username} (مدة: ${connectionDuration}ms)`);
            }

            // محاولة إعادة الاتصال
            console.log(`🔄 بدء إعادة المحاولة للبوت ${this.config.username} بسبب انقطاع الاتصال`);
            this.handleReconnect();
        });

        // عند الركل من السيرفر
        this.bot.on('kicked', (reason, loggedIn) => {
            // تحويل السبب إلى نص إذا كان object
            let kickReason = 'سبب غير معروف';
            if (reason) {
                if (typeof reason === 'string') {
                    kickReason = reason;
                } else if (typeof reason === 'object') {
                    kickReason = reason.text || reason.message || JSON.stringify(reason);
                } else {
                    kickReason = String(reason);
                }
            }

            console.log(`👢 تم ركل البوت من سيرفر Java: ${kickReason}`);

            // تحديد نوع الانقطاع
            const isServerDown = this.isServerDownReason(kickReason);
            const isLoggedInOtherLocation = this.isLoggedInOtherLocationReason(kickReason);
            const isVersionMismatch = kickReason.toLowerCase().includes('outdated');
            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال

            this.emit('kicked', {
                reason: kickReason,
                isServerDown: isServerDown,
                isLoggedInOtherLocation: isLoggedInOtherLocation,
                isVersionMismatch: isVersionMismatch
            });

            // إيقاف محاولات إعادة الاتصال إذا كان السبب هو وجود بوت آخر بنفس الاسم
            if (isLoggedInOtherLocation) {
                console.log(`🚫 إيقاف محاولات إعادة الاتصال للبوت ${this.config.username} - يوجد بوت آخر بنفس الاسم`);
                this.shouldReconnect = false;
                this.stopReconnecting();
                return;
            }

            // إذا كان السبب هو عدم توافق الإصدار، نرسل تحذير خاص
            if (isVersionMismatch) {
                console.log(`⚠️ تحذير: إصدار البوت ${this.config.version} غير متوافق مع السيرفر`);
                console.log(`⚠️ رسالة السيرفر: ${kickReason}`);

                // استخراج الإصدارات المدعومة من رسالة الخطأ
                const supportedVersionsMatch = kickReason.match(/versions: ([0-9., -]+)/);
                if (supportedVersionsMatch) {
                    console.log(`ℹ️ الإصدارات المدعومة: ${supportedVersionsMatch[1]}`);
                }

                // إرسال حدث خاص بعدم توافق الإصدار
                this.emit('versionMismatch', {
                    botVersion: this.config.version,
                    serverMessage: kickReason
                });
            }

            // محاولة إعادة الاتصال (حتى لو كان مشكلة إصدار - قد يتم إصلاحها)
            console.log(`🔄 بدء إعادة المحاولة للبوت ${this.config.username} بسبب: ${kickReason}`);
            this.handleReconnect();
        });

        // عند تأسيس الاتصال
        this.bot.on('connect', () => {
            console.log(`🤝 تم تأسيس الاتصال مع سيرفر Java`);
        });

        // عند تحديث الموقع
        this.bot.on('move', () => {
            if (this.bot.entity) {
                this.playerInfo.position = this.bot.entity.position;
            }
        });

        // عند حدوث خطأ
        this.bot.on('error', (error) => {
            console.error(`❌ خطأ في البوت Java ${this.config.username}: ${error.message}`);
            this.stats.errors++;

            // إذا حدث خطأ، فالبوت ليس متصل فعلياً
            const wasConnected = this.isConnected;
            this.isConnected = false;
            this.isConnecting = false;

            // إذا كان البوت متصل من قبل، أرسل حدث انقطاع
            if (wasConnected) {
                this.disconnectionTime = new Date();
                const connectionDuration = this.connectionTime ? Date.now() - this.connectionTime.getTime() : 0;
                const wasRealConnection = connectionDuration > 3000;

                this.emit('disconnected', {
                    connectionTime: this.connectionTime,
                    disconnectionTime: this.disconnectionTime,
                    duration: this.getSessionDuration(),
                    wasRealConnection: wasRealConnection,
                    connectionDuration: connectionDuration,
                    reason: 'error'
                });
            }

            this.emit('error', error);
        });

        // أحداث إضافية
        this.bot.on('playerJoined', (player) => {
            // تحديث قائمة اللاعبين
            this.emit('playerListUpdate', player);
        });

        this.bot.on('health', () => {
            this.emit('healthUpdate', {
                health: this.bot.health,
                food: this.bot.food,
                saturation: this.bot.foodSaturation
            });
        });

        this.bot.on('death', () => {
            this.stats.deaths++;
            console.log(`💀 مات البوت ${this.config.username}`);
            this.emit('death');
        });

        this.bot.on('respawn', () => {
            this.stats.respawns++;
            console.log(`🔄 تم إحياء البوت ${this.config.username}`);
            this.emit('respawn');
        });
    }

    // إعادة الاتصال
    handleReconnect() {
        if (!this.shouldReconnect) {
            console.log(`🚫 إعادة الاتصال معطلة للبوت ${this.config.username}`);
            return;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log(`❌ تم الوصول للحد الأقصى من محاولات إعادة الاتصال للبوت ${this.config.username}`);
            this.emit('maxReconnectAttemptsReached');
            return;
        }

        this.reconnectAttempts++;
        console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts} للبوت ${this.config.username}`);

        this.reconnectTimeout = setTimeout(() => {
            this.connect();
        }, this.reconnectDelay);
    }

    // إيقاف إعادة الاتصال
    stopReconnecting() {
        this.shouldReconnect = false;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        console.log(`🛑 تم إيقاف إعادة الاتصال للبوت ${this.config.username}`);
    }

    // إرسال رسالة
    sendMessage(message) {
        if (!this.isConnected || !this.bot) {
            console.log(`❌ لا يمكن إرسال الرسالة - البوت غير متصل`);
            return false;
        }

        try {
            this.bot.chat(message);
            this.stats.messagesSent++;
            console.log(`📤 تم إرسال الرسالة: ${message}`);
            return true;
        } catch (error) {
            console.error(`❌ خطأ في إرسال الرسالة: ${error.message}`);
            return false;
        }
    }

    // تنفيذ أمر
    executeCommand(command) {
        if (!this.isConnected || !this.bot) {
            console.log(`❌ لا يمكن تنفيذ الأمر - البوت غير متصل`);
            return false;
        }

        try {
            this.bot.chat(command);
            this.stats.commandsExecuted++;
            console.log(`⚡ تم تنفيذ الأمر: ${command}`);
            return true;
        } catch (error) {
            console.error(`❌ خطأ في تنفيذ الأمر: ${error.message}`);
            return false;
        }
    }

    // قطع الاتصال
    disconnect() {
        console.log(`🔌 قطع الاتصال للبوت ${this.config.username}`);
        this.shouldReconnect = false;
        this.stopReconnecting();

        if (this.bot) {
            this.bot.end();
        }

        this.isConnected = false;
        this.isConnecting = false;
        this.disconnectionTime = new Date();
    }

    // الحصول على مدة الجلسة
    getSessionDuration() {
        if (!this.connectionTime) return 0;
        const endTime = this.disconnectionTime || new Date();
        return Math.floor((endTime - this.connectionTime) / 1000);
    }

    // الحصول على إحصائيات البوت
    getStats() {
        return {
            ...this.stats,
            isConnected: this.isConnected,
            connectionTime: this.connectionTime,
            sessionDuration: this.getSessionDuration(),
            reconnectAttempts: this.reconnectAttempts,
            playerInfo: this.playerInfo
        };
    }

    // فحص إذا كان البوت حي - مثل البيدروك تماماً
    isAlive() {
        return this.isConnected && this.bot && this.bot._client && !this.bot._client.socket?.destroyed;
    }

    // الحصول على مدة التشغيل
    getUptime() {
        if (!this.connectionTime) return 0;
        return Date.now() - this.connectionTime.getTime();
    }

    // التحقق من أسباب انقطاع السيرفر
    isServerDownReason(reason) {
        if (!reason) return false;

        // تحويل السبب إلى نص
        let reasonText = '';
        if (typeof reason === 'string') {
            reasonText = reason;
        } else if (typeof reason === 'object') {
            reasonText = reason.text || reason.message || JSON.stringify(reason);
        } else {
            reasonText = String(reason);
        }

        const serverDownReasons = [
            'ECONNREFUSED',
            'ENOTFOUND',
            'ETIMEDOUT',
            'ECONNRESET',
            'connection timed out',
            'server is down',
            'server offline',
            'no route to host',
            'network unreachable'
        ];
        return serverDownReasons.some(r => reasonText.toLowerCase().includes(r.toLowerCase()));
    }

    // التحقق من تسجيل الدخول في مكان آخر
    isLoggedInOtherLocationReason(reason) {
        if (!reason) return false;

        // تحويل السبب إلى نص
        let reasonText = '';
        if (typeof reason === 'string') {
            reasonText = reason;
        } else if (typeof reason === 'object') {
            reasonText = reason.text || reason.message || JSON.stringify(reason);
        } else {
            reasonText = String(reason);
        }

        const loggedInElsewhereReasons = [
            'logged in from another location',
            'you logged in from another location',
            'duplicate login',
            'already connected',
            'player already online'
        ];
        return loggedInElsewhereReasons.some(r => reasonText.toLowerCase().includes(r.toLowerCase()));
    }
}

module.exports = MinecraftJavaBot;
