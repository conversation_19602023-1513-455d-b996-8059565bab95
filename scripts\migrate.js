#!/usr/bin/env node

/**
 * 🔄 نظام ترقية قاعدة البيانات المتقدم
 * يدعم PostgreSQL و SQLite مع إدارة الإصدارات
 */

const path = require('path');
const fs = require('fs-extra');
const { config } = require('../config');

class MigrationManager {
    constructor() {
        this.migrationsPath = path.join(__dirname, '..', 'migrations');
        this.dbType = this.detectDatabaseType(config.database.url);
        this.db = null;
        this.client = null;
        
        console.log(`🔄 نظام الترقية - نوع قاعدة البيانات: ${this.dbType}`);
    }

    // اكتشاف نوع قاعدة البيانات
    detectDatabaseType(url) {
        if (url.startsWith('postgresql://') || url.startsWith('postgres://')) return 'postgresql';
        return 'sqlite';
    }

    // تهيئة الاتصال
    async init() {
        try {
            if (this.dbType === 'postgresql') {
                const { Pool } = require('pg');
                this.client = new Pool({
                    connectionString: config.database.url,
                    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
                });
                console.log('✅ تم الاتصال بـ PostgreSQL');
            } else {
                const sqlite3 = require('sqlite3').verbose();
                const dbPath = config.database.url.replace('sqlite:', '');
                await fs.ensureDir(path.dirname(dbPath));
                
                this.db = new sqlite3.Database(dbPath);
                console.log('✅ تم الاتصال بـ SQLite');
            }

            await this.createMigrationsTable();
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الترقية:', error);
            throw error;
        }
    }

    // إنشاء جدول الترقيات
    async createMigrationsTable() {
        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                await client.query(`
                    CREATE TABLE IF NOT EXISTS migrations (
                        id SERIAL PRIMARY KEY,
                        version VARCHAR(255) UNIQUE NOT NULL,
                        name VARCHAR(255) NOT NULL,
                        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                `);
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS migrations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        version TEXT UNIQUE NOT NULL,
                        name TEXT NOT NULL,
                        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `, (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        }
    }

    // الحصول على الترقيات المنفذة
    async getExecutedMigrations() {
        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                const result = await client.query('SELECT version FROM migrations ORDER BY version');
                return result.rows.map(row => row.version);
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                this.db.all('SELECT version FROM migrations ORDER BY version', (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows.map(row => row.version));
                });
            });
        }
    }

    // تسجيل ترقية منفذة
    async recordMigration(version, name) {
        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                await client.query(
                    'INSERT INTO migrations (version, name) VALUES ($1, $2)',
                    [version, name]
                );
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(
                    'INSERT INTO migrations (version, name) VALUES (?, ?)',
                    [version, name],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });
        }
    }

    // حذف سجل ترقية
    async removeMigration(version) {
        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                await client.query('DELETE FROM migrations WHERE version = $1', [version]);
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                this.db.run('DELETE FROM migrations WHERE version = ?', [version], (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        }
    }

    // تنفيذ الترقيات
    async migrate(direction = 'up') {
        try {
            console.log(`🔄 بدء عملية الترقية (${direction})...`);
            
            // إنشاء مجلد الترقيات إذا لم يكن موجوداً
            await fs.ensureDir(this.migrationsPath);
            
            const executedMigrations = await this.getExecutedMigrations();
            const migrationFiles = await this.getMigrationFiles();
            
            if (direction === 'up') {
                const pendingMigrations = migrationFiles.filter(
                    file => !executedMigrations.includes(file.version)
                );
                
                if (pendingMigrations.length === 0) {
                    console.log('✅ لا توجد ترقيات جديدة للتنفيذ');
                    return;
                }
                
                for (const migration of pendingMigrations) {
                    await this.executeMigration(migration, 'up');
                }
            } else {
                // التراجع عن آخر ترقية
                const lastMigration = executedMigrations[executedMigrations.length - 1];
                if (lastMigration) {
                    const migrationFile = migrationFiles.find(f => f.version === lastMigration);
                    if (migrationFile) {
                        await this.executeMigration(migrationFile, 'down');
                    }
                }
            }
            
            console.log('✅ تم إكمال عملية الترقية بنجاح');
        } catch (error) {
            console.error('❌ خطأ في عملية الترقية:', error);
            throw error;
        }
    }

    // الحصول على ملفات الترقية
    async getMigrationFiles() {
        const files = await fs.readdir(this.migrationsPath);
        return files
            .filter(file => file.endsWith('.js'))
            .map(file => {
                const version = file.split('_')[0];
                const name = file.replace('.js', '').substring(version.length + 1);
                return { version, name, filename: file };
            })
            .sort((a, b) => a.version.localeCompare(b.version));
    }

    // تنفيذ ترقية واحدة
    async executeMigration(migration, direction) {
        try {
            console.log(`${direction === 'up' ? '⬆️' : '⬇️'} تنفيذ ترقية: ${migration.name}`);
            
            const migrationPath = path.join(this.migrationsPath, migration.filename);
            const migrationModule = require(migrationPath);
            
            if (direction === 'up') {
                await migrationModule.up(this.dbType === 'postgresql' ? this.client : this.db);
                await this.recordMigration(migration.version, migration.name);
                console.log(`✅ تم تنفيذ الترقية: ${migration.name}`);
            } else {
                await migrationModule.down(this.dbType === 'postgresql' ? this.client : this.db);
                await this.removeMigration(migration.version);
                console.log(`✅ تم التراجع عن الترقية: ${migration.name}`);
            }
        } catch (error) {
            console.error(`❌ خطأ في تنفيذ الترقية ${migration.name}:`, error);
            throw error;
        }
    }

    // إغلاق الاتصال
    async close() {
        try {
            if (this.client) {
                await this.client.end();
            } else if (this.db) {
                this.db.close();
            }
        } catch (error) {
            console.error('❌ خطأ في إغلاق الاتصال:', error);
        }
    }
}

// تشغيل النظام
async function main() {
    const direction = process.argv[2] || 'up';
    const migrationManager = new MigrationManager();
    
    try {
        await migrationManager.init();
        await migrationManager.migrate(direction);
    } catch (error) {
        console.error('❌ فشل في عملية الترقية:', error);
        process.exit(1);
    } finally {
        await migrationManager.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = MigrationManager;
