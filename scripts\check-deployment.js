#!/usr/bin/env node

/**
 * 🔍 فحص الملفات المطلوبة للنشر
 * يتحقق من وجود جميع الملفات الضرورية قبل النشر
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص الملفات المطلوبة للنشر...\n');

const requiredFiles = [
    'package.json',
    'package-lock.json',
    'index.js',
    'config.js',
    'logger.js',
    'database.js',
    'bot-manager.js',
    'minecraft-java-bot.js',
    'minecraft-bedrock-bot.js',
    'telegram-bot.js',
    'health-check-server.js',
    'backup-manager.js'
];

const deploymentFiles = [
    'railway.dockerfile',
    'railway.json',
    'nixpacks.toml',
    'start.sh',
    '.nvmrc'
];

let allGood = true;

// فحص الملفات المطلوبة
console.log('📋 الملفات الأساسية:');
requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - مفقود!`);
        allGood = false;
    }
});

console.log('\n🚀 ملفات النشر:');
deploymentFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`⚠️  ${file} - غير موجود`);
    }
});

// فحص package-lock.json
const lockfilePath = path.join(process.cwd(), 'package-lock.json');
if (fs.existsSync(lockfilePath)) {
    try {
        const lockfile = JSON.parse(fs.readFileSync(lockfilePath, 'utf8'));
        console.log(`\n📦 package-lock.json:`);
        console.log(`   - الإصدار: ${lockfile.lockfileVersion}`);
        console.log(`   - اسم المشروع: ${lockfile.name}`);
        console.log(`   - إصدار المشروع: ${lockfile.version}`);
    } catch (error) {
        console.log(`❌ خطأ في قراءة package-lock.json: ${error.message}`);
        allGood = false;
    }
}

console.log('\n' + '='.repeat(50));
if (allGood) {
    console.log('🎉 جميع الملفات المطلوبة موجودة! جاهز للنشر.');
    process.exit(0);
} else {
    console.log('❌ بعض الملفات المطلوبة مفقودة. يرجى إصلاح المشاكل قبل النشر.');
    process.exit(1);
}
