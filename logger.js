/**
 * 📝 نظام السجلات المتقدم
 * تسجيل شامل للأحداث مع دعم ملفات متعددة ومستويات مختلفة
 */

const fs = require('fs-extra');
const path = require('path');
const { EventEmitter } = require('events');
const { config } = require('./config');

class Logger extends EventEmitter {
    constructor() {
        super();
        this.logDir = config.logging.file.path || './logs/';
        this.maxFileSize = config.logging.file.maxSize || 10485760; // 10MB
        this.maxFiles = config.logging.file.maxFiles || 5;
        this.logLevel = config.logging.level || 'info';
        this.consoleEnabled = config.logging.console.enabled;
        this.fileEnabled = config.logging.file.enabled;
        this.colorsEnabled = config.logging.console.colors;
        this.timestampEnabled = config.logging.console.timestamp;
        
        this.logLevels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3,
            trace: 4
        };

        this.colors = {
            error: '\x1b[31m',   // أحمر
            warn: '\x1b[33m',    // أصفر
            info: '\x1b[36m',    // سماوي
            debug: '\x1b[35m',   // بنفسجي
            trace: '\x1b[37m',   // أبيض
            reset: '\x1b[0m'     // إعادة تعيين
        };

        this.currentLogFile = null;
        this.currentFileSize = 0;
        this.initialized = false;
    }

    // تهيئة نظام السجلات
    async init() {
        if (this.initialized) return this;

        try {
            console.log('📝 تهيئة نظام السجلات...');

            // إنشاء مجلد السجلات
            if (this.fileEnabled) {
                await fs.ensureDir(this.logDir);
                await this.initializeLogFile();
            }

            // تسجيل بداية النظام
            this.info('نظام السجلات', 'تم بدء تشغيل نظام السجلات');

            this.initialized = true;
            console.log('✅ تم تهيئة نظام السجلات بنجاح');

            return this;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام السجلات:', error);
            throw error;
        }
    }

    // تهيئة ملف السجل
    async initializeLogFile() {
        const dateStr = new Date().toISOString().split('T')[0];
        this.currentLogFile = path.join(this.logDir, `app-${dateStr}.log`);

        // فحص حجم الملف الحالي
        if (await fs.pathExists(this.currentLogFile)) {
            const stats = await fs.stat(this.currentLogFile);
            this.currentFileSize = stats.size;

            // إذا كان الملف كبيراً، أنشئ ملف جديد
            if (this.currentFileSize >= this.maxFileSize) {
                await this.rotateLogFile();
            }
        } else {
            this.currentFileSize = 0;
        }
    }

    // دوران ملفات السجل
    async rotateLogFile() {
        try {
            const dateStr = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const rotatedFile = path.join(this.logDir, `app-${dateStr}-${timestamp}.log`);

            // نقل الملف الحالي
            if (await fs.pathExists(this.currentLogFile)) {
                await fs.move(this.currentLogFile, rotatedFile);
            }

            // إنشاء ملف جديد
            this.currentLogFile = path.join(this.logDir, `app-${dateStr}.log`);
            this.currentFileSize = 0;

            // تنظيف الملفات القديمة
            await this.cleanupOldLogFiles();

            this.info('نظام السجلات', `تم دوران ملف السجل: ${rotatedFile}`);
        } catch (error) {
            console.error('❌ خطأ في دوران ملف السجل:', error);
        }
    }

    // تنظيف ملفات السجل القديمة
    async cleanupOldLogFiles() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files
                .filter(file => file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDir, file),
                    stats: null
                }));

            // الحصول على معلومات الملفات
            for (const file of logFiles) {
                try {
                    file.stats = await fs.stat(file.path);
                } catch (error) {
                    // تجاهل الملفات التي لا يمكن قراءتها
                }
            }

            // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
            const validFiles = logFiles
                .filter(file => file.stats)
                .sort((a, b) => b.stats.mtime - a.stats.mtime);

            // حذف الملفات الزائدة
            if (validFiles.length > this.maxFiles) {
                const filesToDelete = validFiles.slice(this.maxFiles);
                for (const file of filesToDelete) {
                    try {
                        await fs.remove(file.path);
                        console.log(`🗑️ تم حذف ملف السجل القديم: ${file.name}`);
                    } catch (error) {
                        console.error(`❌ خطأ في حذف ملف السجل ${file.name}:`, error);
                    }
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تنظيف ملفات السجل:', error);
        }
    }

    // تسجيل رسالة
    async log(level, category, message, data = null) {
        // فحص مستوى السجل
        if (this.logLevels[level] > this.logLevels[this.logLevel]) {
            return;
        }

        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            category,
            message,
            data,
            pid: process.pid
        };

        // تسجيل في وحدة التحكم
        if (this.consoleEnabled) {
            this.logToConsole(logEntry);
        }

        // تسجيل في الملف
        if (this.fileEnabled) {
            await this.logToFile(logEntry);
        }

        // إرسال حدث
        this.emit('log', logEntry);
    }

    // تسجيل في وحدة التحكم
    logToConsole(logEntry) {
        let output = '';

        // إضافة الوقت
        if (this.timestampEnabled) {
            const time = new Date(logEntry.timestamp).toLocaleTimeString('ar-EG');
            output += `[${time}] `;
        }

        // إضافة المستوى مع الألوان
        if (this.colorsEnabled) {
            const color = this.colors[logEntry.level.toLowerCase()] || this.colors.info;
            output += `${color}${logEntry.level}${this.colors.reset} `;
        } else {
            output += `${logEntry.level} `;
        }

        // إضافة الفئة
        output += `[${logEntry.category}] `;

        // إضافة الرسالة
        output += logEntry.message;

        // إضافة البيانات الإضافية
        if (logEntry.data) {
            output += ` | البيانات: ${JSON.stringify(logEntry.data)}`;
        }

        console.log(output);
    }

    // تسجيل في الملف
    async logToFile(logEntry) {
        try {
            // فحص حجم الملف
            if (this.currentFileSize >= this.maxFileSize) {
                await this.rotateLogFile();
            }

            // تنسيق السجل للملف
            const logLine = JSON.stringify(logEntry) + '\n';

            // كتابة في الملف
            await fs.appendFile(this.currentLogFile, logLine);
            this.currentFileSize += Buffer.byteLength(logLine);

        } catch (error) {
            console.error('❌ خطأ في كتابة السجل في الملف:', error);
        }
    }

    // دوال التسجيل المختصرة
    error(category, message, data = null) {
        return this.log('error', category, message, data);
    }

    warn(category, message, data = null) {
        return this.log('warn', category, message, data);
    }

    info(category, message, data = null) {
        return this.log('info', category, message, data);
    }

    debug(category, message, data = null) {
        return this.log('debug', category, message, data);
    }

    trace(category, message, data = null) {
        return this.log('trace', category, message, data);
    }

    // تسجيل خطأ مع stack trace
    async logError(category, error, additionalData = null) {
        const errorData = {
            name: error.name,
            message: error.message,
            stack: error.stack,
            ...additionalData
        };

        await this.error(category, `خطأ: ${error.message}`, errorData);
    }

    // تسجيل أداء العملية
    async logPerformance(category, operation, duration, additionalData = null) {
        const performanceData = {
            operation,
            duration: `${duration}ms`,
            ...additionalData
        };

        await this.info(category, `أداء العملية: ${operation}`, performanceData);
    }

    // تسجيل نشاط المستخدم
    async logUserActivity(userId, action, details = null) {
        const activityData = {
            userId,
            action,
            timestamp: new Date().toISOString(),
            ...details
        };

        await this.info('نشاط المستخدم', `المستخدم ${userId}: ${action}`, activityData);
    }

    // تسجيل نشاط البوت
    async logBotActivity(botId, botName, action, details = null) {
        const botData = {
            botId,
            botName,
            action,
            timestamp: new Date().toISOString(),
            ...details
        };

        await this.info('نشاط البوت', `البوت ${botName}: ${action}`, botData);
    }

    // البحث في السجلات
    async searchLogs(query, options = {}) {
        try {
            const {
                level = null,
                category = null,
                startDate = null,
                endDate = null,
                limit = 100
            } = options;

            if (!this.fileEnabled) {
                throw new Error('البحث في السجلات متاح فقط عند تفعيل تسجيل الملفات');
            }

            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            const results = [];

            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const content = await fs.readFile(filePath, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        const logEntry = JSON.parse(line);

                        // تطبيق المرشحات
                        if (level && logEntry.level.toLowerCase() !== level.toLowerCase()) continue;
                        if (category && logEntry.category !== category) continue;
                        if (startDate && new Date(logEntry.timestamp) < new Date(startDate)) continue;
                        if (endDate && new Date(logEntry.timestamp) > new Date(endDate)) continue;

                        // البحث في النص
                        const searchText = `${logEntry.message} ${JSON.stringify(logEntry.data || {})}`.toLowerCase();
                        if (query && !searchText.includes(query.toLowerCase())) continue;

                        results.push(logEntry);

                        if (results.length >= limit) break;
                    } catch (error) {
                        // تجاهل الأسطر التي لا يمكن تحليلها
                    }
                }

                if (results.length >= limit) break;
            }

            return results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        } catch (error) {
            console.error('❌ خطأ في البحث في السجلات:', error);
            throw error;
        }
    }

    // الحصول على إحصائيات السجلات
    async getLogStats() {
        try {
            const stats = {
                totalFiles: 0,
                totalSize: 0,
                oldestLog: null,
                newestLog: null,
                levelCounts: {
                    error: 0,
                    warn: 0,
                    info: 0,
                    debug: 0,
                    trace: 0
                }
            };

            if (!this.fileEnabled) {
                return stats;
            }

            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            stats.totalFiles = logFiles.length;

            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const fileStats = await fs.stat(filePath);
                stats.totalSize += fileStats.size;

                // تحديث أقدم وأحدث سجل
                if (!stats.oldestLog || fileStats.mtime < stats.oldestLog) {
                    stats.oldestLog = fileStats.mtime;
                }
                if (!stats.newestLog || fileStats.mtime > stats.newestLog) {
                    stats.newestLog = fileStats.mtime;
                }

                // عد مستويات السجل (عينة من أول 1000 سطر)
                try {
                    const content = await fs.readFile(filePath, 'utf8');
                    const lines = content.split('\n').slice(0, 1000);

                    for (const line of lines) {
                        try {
                            const logEntry = JSON.parse(line);
                            const level = logEntry.level.toLowerCase();
                            if (stats.levelCounts[level] !== undefined) {
                                stats.levelCounts[level]++;
                            }
                        } catch (error) {
                            // تجاهل الأسطر التي لا يمكن تحليلها
                        }
                    }
                } catch (error) {
                    // تجاهل الملفات التي لا يمكن قراءتها
                }
            }

            return stats;

        } catch (error) {
            console.error('❌ خطأ في الحصول على إحصائيات السجلات:', error);
            throw error;
        }
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إغلاق نظام السجلات
    async close() {
        try {
            console.log('📝 إغلاق نظام السجلات...');

            // تسجيل إغلاق النظام
            if (this.initialized) {
                await this.info('نظام السجلات', 'تم إغلاق نظام السجلات');
            }

            console.log('✅ تم إغلاق نظام السجلات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إغلاق نظام السجلات:', error);
        }
    }
}

module.exports = Logger;
