# Nixpacks Configuration for Railway
# تكوين Nixpacks لـ Railway

[phases.setup]
nixPkgs = [
  "nodejs-18_x",
  "npm-9_x",
  "curl",
  "python3",
  "gcc",
  "gnumake",
  "cmake",
  "git"
]

[phases.install]
cmds = [
  "export PYTHON=/usr/bin/python3",
  "npm ci --omit=dev --verbose",
  "npm cache clean --force"
]

[phases.build]
cmds = [
  "mkdir -p logs backups temp data uploads"
]

[start]
cmd = "npm start"

[variables]
NODE_ENV = "production"
PORT = "3001"
PYTHON = "/usr/bin/python3"
CXX = "g++"
CC = "gcc"
