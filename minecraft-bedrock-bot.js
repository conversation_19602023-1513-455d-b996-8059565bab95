/**
 * 🪨 بوت ماينكرافت Bedrock Edition المحسن
 * دعم كامل لجميع ميزات Bedrock Edition مع إعادة اتصال ذكية
 */

const bedrock = require('bedrock-protocol');
const { EventEmitter } = require('events');
const { config } = require('./config');

class MinecraftBedrockBot extends EventEmitter {
    constructor(botConfig) {
        super();
        this.config = botConfig;
        this.client = null;
        this.isConnected = false;
        this.connectionTime = null;
        this.disconnectionTime = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 30; // 5 دقائق × 6 محاولات/دقيقة
        this.reconnectDelay = config.bots.reconnection.delay;
        this.shouldReconnect = true;
        this.reconnectTimeout = null;
        this.isConnecting = false; // منع المحاولات المتعددة
        this.playerInfo = {};
        this.stats = {
            messagesSent: 0,
            commandsExecuted: 0,
            deaths: 0,
            respawns: 0,
            errors: 0
        };
        
        console.log(`🔧 تم إنشاء بوت Bedrock: ${this.config.username}`);
    }

    // الاتصال بالسيرفر
    async connect() {
        // منع المحاولات المتعددة
        if (this.isConnecting) {
            console.log(`⚠️ محاولة اتصال جارية بالفعل للبوت ${this.config.username}`);
            return;
        }

        this.isConnecting = true;

        try {
            console.log(`🔄 محاولة الاتصال بسيرفر Bedrock: ${this.config.host}:${this.config.port}`);
            console.log(`📦 إصدار البوت: ${this.config.version}`);
            
            const clientOptions = {
                host: this.config.host,
                port: this.config.port,
                username: this.config.username,
                version: this.config.version,
                offline: true, // للسيرفرات التي لا تتطلب مصادقة Xbox Live
                skipPing: true,
                keepAlive: true,
                connectTimeout: config.bots.timeout.connection,
                onMsaCode: (data) => {
                    console.log(`🔐 كود المصادقة: ${data.user_code}`);
                    console.log(`🌐 اذهب إلى: ${data.verification_uri}`);
                }
            };

            // إضافة مصادقة Xbox Live إذا كانت متطلبة
            if (this.config.xboxAuth) {
                clientOptions.offline = false;
                clientOptions.authTitle = '00000000441cc96b'; // Minecraft Bedrock Edition
            }

            // استخدام الإصدار الذي اختاره المستخدم دائماً
            clientOptions.version = this.config.version;
            console.log(`🎯 استخدام إصدار البوت المحدد: ${this.config.version}`);

            this.client = bedrock.createClient(clientOptions);
            this.setupEventHandlers();

            // إضافة معالج للأخطاء الأولية
            this.client.on('error', (error) => {
                this.isConnecting = false; // إنهاء حالة الاتصال
                if (!this.isConnected) {
                    console.log(`❌ فشل الاتصال الأولي: ${error.message}`);
                    // بدء إعادة المحاولة إذا كان السبب يشير لسيرفر مطفي
                    if (this.isServerDownReason(error.message)) {
                        this.handleReconnect();
                    }
                }
            });

        } catch (error) {
            this.isConnecting = false; // إنهاء حالة الاتصال
            console.error(`❌ خطأ في إنشاء بوت Bedrock: ${error.message}`);
            // بدء إعادة المحاولة إذا كان السبب يشير لسيرفر مطفي
            if (this.isServerDownReason(error.message)) {
                this.handleReconnect();
            } else {
                this.emit('error', error);
            }
        }
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // عند الاتصال بنجاح
        this.client.on('join', () => {
            console.log(`✅ تم الاتصال بنجاح بسيرفر Bedrock ${this.config.host}:${this.config.port}`);
            this.isConnected = true;
            this.isConnecting = false; // إنهاء حالة الاتصال
            this.connectionTime = new Date();
            this.reconnectAttempts = 0;

            // تنظيف timeout إعادة الاتصال إذا كان موجود
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = null;
            }
            
            this.emit('connected', {
                connectionTime: this.connectionTime,
                serverInfo: {
                    host: this.config.host,
                    port: this.config.port,
                    version: this.config.version
                }
            });
        });

        // عند دخول العالم
        this.client.on('spawn', () => {
            console.log(`🌍 دخل البوت ${this.config.username} إلى عالم Bedrock`);
            this.emit('worldJoined', {
                position: this.playerInfo.position || null,
                dimension: this.playerInfo.dimension || null
            });
        });

        // عند استقبال رسالة في الشات
        this.client.on('text', (packet) => {
            if (packet.type === 'chat' && packet.source_name !== this.config.username) {
                console.log(`💬 [${packet.source_name}]: ${packet.message}`);
                this.emit('chatMessage', {
                    username: packet.source_name,
                    message: packet.message,
                    timestamp: new Date()
                });
            }
        });

        // عند انقطاع الاتصال
        this.client.on('close', () => {
            console.log(`🔌 السيرفر ${this.config.host}:${this.config.port} مطفي أو غير متصل`);
            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال
            this.disconnectionTime = new Date();

            this.emit('disconnected', {
                connectionTime: this.connectionTime,
                disconnectionTime: this.disconnectionTime,
                duration: this.getSessionDuration()
            });

            // محاولة إعادة الاتصال
            console.log(`🔄 بدء إعادة المحاولة للبوت ${this.config.username} بسبب انقطاع الاتصال`);
            this.handleReconnect();
        });

        // عند الركل من السيرفر
        this.client.on('disconnect', (packet) => {
            const reason = packet.message || 'سبب غير معروف';
            console.log(`👢 تم ركل البوت من سيرفر Bedrock: ${reason}`);

            // تحديد نوع الانقطاع
            const isServerDown = this.isServerDownReason(reason);
            const isLoggedInOtherLocation = this.isLoggedInOtherLocationReason(reason);
            const isVersionMismatch = reason.toLowerCase().includes('outdated');
            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال

            this.emit('kicked', {
                reason: reason,
                isServerDown: isServerDown,
                isLoggedInOtherLocation: isLoggedInOtherLocation,
                isVersionMismatch: isVersionMismatch
            });

            // إيقاف محاولات إعادة الاتصال إذا كان السبب هو وجود بوت آخر بنفس الاسم
            if (isLoggedInOtherLocation) {
                console.log(`🚫 إيقاف محاولات إعادة الاتصال للبوت ${this.config.username} - يوجد بوت آخر بنفس الاسم`);
                this.shouldReconnect = false;
                this.stopReconnecting();
                return;
            }

            // إذا كان السبب هو عدم توافق الإصدار، نرسل تحذير خاص
            if (isVersionMismatch) {
                console.log(`⚠️ تحذير: إصدار البوت ${this.config.version} غير متوافق مع السيرفر`);
                console.log(`⚠️ رسالة السيرفر: ${reason}`);

                // استخراج الإصدارات المدعومة من رسالة الخطأ
                const supportedVersionsMatch = reason.match(/versions: ([0-9., -]+)/);
                if (supportedVersionsMatch) {
                    console.log(`ℹ️ الإصدارات المدعومة: ${supportedVersionsMatch[1]}`);
                }

                // إرسال حدث خاص بعدم توافق الإصدار
                this.emit('versionMismatch', {
                    botVersion: this.config.version,
                    serverMessage: reason
                });
            }

            // محاولة إعادة الاتصال (حتى لو كان مشكلة إصدار - قد يتم إصلاحها)
            console.log(`🔄 بدء إعادة المحاولة للبوت ${this.config.username} بسبب: ${reason}`);
            this.handleReconnect();
        });

        // عند تأسيس الاتصال
        this.client.on('server_to_client_handshake', () => {
            console.log(`🤝 تم تأسيس الاتصال مع سيرفر Bedrock`);
        });

        // عند بدء اللعبة
        this.client.on('start_game', (packet) => {
            console.log(`🎮 بدء اللعبة في عالم Bedrock`);
            this.playerInfo = {
                entityId: packet.runtime_entity_id,
                position: packet.player_position,
                dimension: packet.dimension
            };

            this.emit('gameStarted', {
                playerInfo: this.playerInfo,
                gameMode: packet.player_gamemode,
                dimension: packet.dimension
            });

            // إرسال حدث دخول العالم مباشرة بعد بدء اللعبة
            console.log(`🌍 دخل البوت ${this.config.username} إلى عالم Bedrock`);
            this.emit('worldJoined', {
                username: this.config.username,
                host: this.config.host,
                port: this.config.port
            });
        });

        // عند دخول العالم (معالج إضافي)
        this.client.on('spawn', () => {
            console.log(`🌍 تأكيد دخول البوت ${this.config.username} إلى عالم Bedrock`);
            // لا نرسل الحدث مرة أخرى لتجنب التكرار
        });

        // عند تحديث الموقع
        this.client.on('move_player', (packet) => {
            if (packet.runtime_entity_id === this.playerInfo.entityId) {
                this.playerInfo.position = packet.position;
            }
        });

        // عند حدوث خطأ
        this.client.on('error', (error) => {
            console.error(`❌ خطأ في البوت Bedrock ${this.config.username}: ${error.message}`);
            this.stats.errors++;
            this.emit('error', error);
        });

        // أحداث إضافية
        this.client.on('player_list', (packet) => {
            // تحديث قائمة اللاعبين
            this.emit('playerListUpdate', packet);
        });

        this.client.on('set_health', (packet) => {
            this.emit('healthUpdate', {
                health: packet.health
            });
        });
    }

    // معالجة إعادة الاتصال
    handleReconnect() {
        console.log(`🔍 فحص إمكانية إعادة الاتصال للبوت ${this.config.username}:`);
        console.log(`   - isConnecting: ${this.isConnecting}`);
        console.log(`   - isConnected: ${this.isConnected}`);
        console.log(`   - shouldReconnect: ${this.shouldReconnect}`);
        console.log(`   - reconnectAttempts: ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

        // تجنب المحاولات المتعددة
        if (this.isConnecting || this.isConnected) {
            console.log(`⚠️ تجاهل إعادة الاتصال - البوت ${this.isConnecting ? 'يحاول الاتصال' : 'متصل'} بالفعل`);
            return;
        }

        if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                console.log(`🛑 تم الوصول للحد الأقصى من محاولات إعادة الاتصال للبوت ${this.config.username}`);
                this.emit('maxReconnectAttemptsReached');
            } else {
                console.log(`🛑 إعادة الاتصال معطلة للبوت ${this.config.username}`);
            }
            return;
        }

        // إلغاء أي محاولة إعادة اتصال سابقة
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }

        this.reconnectAttempts++;
        const delay = this.calculateReconnectDelay();

        console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts} خلال ${delay / 1000} ثواني...`);

        this.reconnectTimeout = setTimeout(async () => {
            // التحقق مرة أخرى قبل المحاولة
            if (this.isConnected) {
                console.log(`✅ البوت ${this.config.username} متصل بالفعل - إلغاء إعادة المحاولة`);
                this.reconnectAttempts = 0; // إعادة تعيين العداد عند نجاح الاتصال
                return;
            }

            // تنظيف الاتصال السابق إذا كان موجود
            if (this.client) {
                try {
                    this.client.removeAllListeners();
                    this.client.close();
                } catch (error) {
                    console.log(`⚠️ خطأ في تنظيف العميل السابق: ${error.message}`);
                }
                this.client = null;
            }

            try {
                await this.connect();
            } catch (error) {
                console.error(`❌ فشل في إعادة الاتصال: ${error.message}`);
                // محاولة أخرى إذا فشلت
                if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.handleReconnect();
                }
            }
        }, delay);
    }

    // حساب تأخير إعادة الاتصال (ثابت 10 ثواني)
    calculateReconnectDelay() {
        return 10000; // 10 ثواني ثابتة كما طلب المستخدم
    }

    // إيقاف محاولات إعادة الاتصال
    stopReconnecting() {
        this.shouldReconnect = false;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        console.log(`🛑 تم إيقاف محاولات إعادة الاتصال للبوت ${this.config.username}`);
    }

    // فحص إذا كان السبب يعني أن السيرفر مطفي
    isServerDownReason(reason) {
        if (!reason) return true;
        
        const reasonLower = reason.toLowerCase();
        
        // أسباب تعني أن السيرفر مطفي
        const serverDownReasons = [
            'connection refused',
            'connection timed out',
            'connection reset',
            'server closed',
            'end of stream',
            'socket hang up',
            'network error',
            'timeout',
            'econnrefused',
            'enotfound',
            'etimedout',
            'outdated client',
            'outdated server'
        ];

        // أسباب أخرى لا تعني أن السيرفر مطفي
        const notServerDownReasons = [
            'please log into xbox',
            'loggedinotherlocation',
            'logged in other location',
            'disconnectionscreen.loggedinotherlocation',
            'authentication',
            'xbox live',
            'microsoft account',
            'premium account',
            'whitelist',
            'banned',
            'kicked',
            'full server'
        ];

        // فحص الأسباب التي لا تعني أن السيرفر مطفي أولاً
        for (const notServerReason of notServerDownReasons) {
            if (reasonLower.includes(notServerReason)) {
                return false;
            }
        }

        // فحص الأسباب التي تعني أن السيرفر مطفي
        for (const serverReason of serverDownReasons) {
            if (reasonLower.includes(serverReason)) {
                return true;
            }
        }

        // افتراضياً، إذا لم نتمكن من تحديد السبب، نعتبر أن السيرفر مطفي
        return true;
    }

    // فحص إذا كان السبب هو تسجيل دخول من مكان آخر
    isLoggedInOtherLocationReason(reason) {
        if (!reason) return false;
        
        const reasonLower = reason.toLowerCase();
        
        const loggedInOtherLocationReasons = [
            'loggedinotherlocation',
            'logged in other location',
            'disconnectionscreen.loggedinotherlocation',
            'another location',
            'duplicate login',
            'already connected'
        ];

        return loggedInOtherLocationReasons.some(otherReason => reasonLower.includes(otherReason));
    }

    // إرسال رسالة في الشات
    sendMessage(message) {
        if (this.isConnected && this.client) {
            try {
                // التحقق من طول الرسالة
                if (message.length > config.game.limits.maxMessageLength) {
                    message = message.substring(0, config.game.limits.maxMessageLength);
                }
                
                this.client.write('text', {
                    type: 'chat',
                    needs_translation: false,
                    source_name: this.config.username,
                    message: message,
                    parameters: [],
                    xuid: '',
                    platform_chat_id: ''
                });
                
                this.stats.messagesSent++;
                console.log(`📤 تم إرسال رسالة: ${message}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في إرسال الرسالة: ${error.message}`);
                return false;
            }
        }
        return false;
    }

    // تنفيذ أمر
    executeCommand(command) {
        if (this.isConnected && this.client) {
            try {
                // التحقق من طول الأمر
                if (command.length > config.game.limits.maxCommandLength) {
                    command = command.substring(0, config.game.limits.maxCommandLength);
                }
                
                // إضافة / إذا لم تكن موجودة
                if (!command.startsWith('/')) {
                    command = '/' + command;
                }
                
                this.client.write('command_request', {
                    command: command,
                    origin: {
                        type: 'player',
                        uuid: this.client.profile?.uuid || '',
                        request_id: ''
                    },
                    internal: false,
                    version: 1
                });
                
                this.stats.commandsExecuted++;
                console.log(`⚡ تم تنفيذ أمر Bedrock: ${command}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في تنفيذ الأمر: ${error.message}`);
                return false;
            }
        }
        return false;
    }

    // الحصول على معلومات البوت
    getBotInfo() {
        if (!this.client) return null;
        
        return {
            username: this.config.username,
            position: this.playerInfo.position || null,
            dimension: this.playerInfo.dimension || null,
            entityId: this.playerInfo.entityId || null
        };
    }

    // الحصول على مدة الجلسة
    getSessionDuration() {
        if (!this.connectionTime) return 0;
        const endTime = this.disconnectionTime || new Date();
        return endTime - this.connectionTime;
    }

    // الحصول على وقت التشغيل
    getUptime() {
        if (!this.connectionTime || !this.isConnected) return 0;
        return Date.now() - this.connectionTime.getTime();
    }

    // الحصول على الإحصائيات
    getStats() {
        return {
            ...this.stats,
            uptime: this.getUptime(),
            sessionDuration: this.getSessionDuration(),
            reconnectAttempts: this.reconnectAttempts,
            isConnected: this.isConnected
        };
    }

    // فحص إذا كان البوت حي
    isAlive() {
        return this.isConnected && this.client && !this.client.closed;
    }

    // قطع الاتصال
    async disconnect() {
        console.log(`🔌 قطع الاتصال مع البوت Bedrock ${this.config.username}`);

        // إيقاف إعادة الاتصال
        this.shouldReconnect = false;
        this.isConnecting = false; // إيقاف حالة الاتصال
        this.stopReconnecting();

        if (this.client) {
            try {
                this.client.disconnect('تم إيقاف البوت');
            } catch (error) {
                console.error(`خطأ في قطع الاتصال: ${error.message}`);
            }
            this.client = null;
        }

        this.isConnected = false;
        this.disconnectionTime = new Date();
        this.reconnectAttempts = this.maxReconnectAttempts; // منع إعادة الاتصال التلقائي
    }

    // تحديث إعدادات البوت
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
}

module.exports = MinecraftBedrockBot;
