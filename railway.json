{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "railway.dockerfile"}, "deploy": {"startCommand": "npm start", "healthcheckPath": "/health", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "environments": {"production": {"variables": {"NODE_ENV": "production", "LOG_LEVEL": "info", "CONSOLE_LOGGING": "true", "FILE_LOGGING": "false", "PYTHON": "/usr/bin/python3", "CXX": "g++", "CC": "gcc"}}}}