/**
 * 🗄️ الترقية الأولية - إنشاء المخطط الأساسي
 * تاريخ الإنشاء: 2024-01-01
 */

module.exports = {
    // تنفيذ الترقية
    async up(db) {
        console.log('📋 إنشاء المخطط الأساسي...');
        
        // تحديد نوع قاعدة البيانات
        const isPostgreSQL = db.constructor.name === 'Pool';
        
        if (isPostgreSQL) {
            // PostgreSQL
            const client = await db.connect();
            try {
                // جدول المستخدمين
                await client.query(`
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        telegram_id BIGINT UNIQUE NOT NULL,
                        username VARCHAR(255),
                        first_name VARCHAR(255),
                        last_name VA<PERSON>HA<PERSON>(255),
                        language_code VARCHAR(10) DEFAULT 'ar',
                        is_admin B<PERSON>OLEAN DEFAULT FALSE,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول البوتات
                await client.query(`
                    CREATE TABLE IF NOT EXISTS bots (
                        id SERIAL PRIMARY KEY,
                        user_id BIGINT NOT NULL,
                        bot_name VARCHAR(255) NOT NULL,
                        server_host VARCHAR(255) NOT NULL,
                        server_port INTEGER NOT NULL,
                        minecraft_version VARCHAR(50) NOT NULL,
                        edition VARCHAR(20) NOT NULL CHECK(edition IN ('java', 'bedrock')),
                        status VARCHAR(20) DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error', 'created')),
                        auto_reconnect BOOLEAN DEFAULT TRUE,
                        auto_respawn BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_connected TIMESTAMP,
                        connection_count INTEGER DEFAULT 0,
                        FOREIGN KEY (user_id) REFERENCES users (telegram_id) ON DELETE CASCADE,
                        UNIQUE(user_id, bot_name, server_host, server_port)
                    )
                `);

                // جدول إحصائيات البوتات
                await client.query(`
                    CREATE TABLE IF NOT EXISTS bot_stats (
                        id SERIAL PRIMARY KEY,
                        bot_id INTEGER NOT NULL,
                        connected_at TIMESTAMP NOT NULL,
                        disconnected_at TIMESTAMP,
                        duration BIGINT,
                        disconnect_reason TEXT,
                        messages_sent INTEGER DEFAULT 0,
                        commands_executed INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
                    )
                `);

                // جدول إعدادات النظام
                await client.query(`
                    CREATE TABLE IF NOT EXISTS settings (
                        id SERIAL PRIMARY KEY,
                        key VARCHAR(255) UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        value_type VARCHAR(20) DEFAULT 'string' CHECK(value_type IN ('string', 'number', 'boolean', 'json')),
                        category VARCHAR(100) DEFAULT 'general',
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول النسخ الاحتياطي
                await client.query(`
                    CREATE TABLE IF NOT EXISTS backups (
                        id SERIAL PRIMARY KEY,
                        filename VARCHAR(255) NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size BIGINT NOT NULL,
                        backup_type VARCHAR(20) NOT NULL CHECK(backup_type IN ('manual', 'auto', 'scheduled')),
                        compression BOOLEAN DEFAULT TRUE,
                        encryption BOOLEAN DEFAULT FALSE,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
                    )
                `);

                // إنشاء الفهارس
                await client.query('CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bots_user_id ON bots(user_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bots_status ON bots(status)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bot_stats_bot_id ON bot_stats(bot_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category)');

                console.log('✅ تم إنشاء مخطط PostgreSQL بنجاح');
            } finally {
                client.release();
            }
        } else {
            // SQLite
            return new Promise((resolve, reject) => {
                db.serialize(() => {
                    // جدول المستخدمين
                    db.run(`
                        CREATE TABLE IF NOT EXISTS users (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            telegram_id INTEGER UNIQUE NOT NULL,
                            username TEXT,
                            first_name TEXT,
                            last_name TEXT,
                            language_code TEXT DEFAULT 'ar',
                            is_admin BOOLEAN DEFAULT 0,
                            is_active BOOLEAN DEFAULT 1,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    `);

                    // جدول البوتات
                    db.run(`
                        CREATE TABLE IF NOT EXISTS bots (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            bot_name TEXT NOT NULL,
                            server_host TEXT NOT NULL,
                            server_port INTEGER NOT NULL,
                            minecraft_version TEXT NOT NULL,
                            edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                            status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error', 'created')),
                            auto_reconnect BOOLEAN DEFAULT 1,
                            auto_respawn BOOLEAN DEFAULT 1,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            last_connected DATETIME,
                            connection_count INTEGER DEFAULT 0,
                            FOREIGN KEY (user_id) REFERENCES users (telegram_id) ON DELETE CASCADE,
                            UNIQUE(user_id, bot_name, server_host, server_port)
                        )
                    `);

                    // جدول إحصائيات البوتات
                    db.run(`
                        CREATE TABLE IF NOT EXISTS bot_stats (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            bot_id INTEGER NOT NULL,
                            connected_at DATETIME NOT NULL,
                            disconnected_at DATETIME,
                            duration INTEGER,
                            disconnect_reason TEXT,
                            messages_sent INTEGER DEFAULT 0,
                            commands_executed INTEGER DEFAULT 0,
                            errors_count INTEGER DEFAULT 0,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
                        )
                    `);

                    // جدول إعدادات النظام
                    db.run(`
                        CREATE TABLE IF NOT EXISTS settings (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            key TEXT UNIQUE NOT NULL,
                            value TEXT NOT NULL,
                            value_type TEXT DEFAULT 'string' CHECK(value_type IN ('string', 'number', 'boolean', 'json')),
                            category TEXT DEFAULT 'general',
                            description TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    `);

                    // جدول النسخ الاحتياطي
                    db.run(`
                        CREATE TABLE IF NOT EXISTS backups (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            filename TEXT NOT NULL,
                            file_path TEXT NOT NULL,
                            file_size INTEGER NOT NULL,
                            backup_type TEXT NOT NULL CHECK(backup_type IN ('manual', 'auto', 'scheduled')),
                            compression BOOLEAN DEFAULT 1,
                            encryption BOOLEAN DEFAULT 0,
                            created_by INTEGER,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else {
                            console.log('✅ تم إنشاء مخطط SQLite بنجاح');
                            resolve();
                        }
                    });
                });
            });
        }
    },

    // التراجع عن الترقية
    async down(db) {
        console.log('🗑️ حذف المخطط الأساسي...');
        
        const isPostgreSQL = db.constructor.name === 'Pool';
        const tables = ['backups', 'bot_stats', 'bots', 'users', 'settings'];
        
        if (isPostgreSQL) {
            const client = await db.connect();
            try {
                for (const table of tables) {
                    await client.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
                }
                console.log('✅ تم حذف مخطط PostgreSQL');
            } finally {
                client.release();
            }
        } else {
            return new Promise((resolve, reject) => {
                db.serialize(() => {
                    let completed = 0;
                    tables.forEach(table => {
                        db.run(`DROP TABLE IF EXISTS ${table}`, (err) => {
                            if (err) reject(err);
                            else {
                                completed++;
                                if (completed === tables.length) {
                                    console.log('✅ تم حذف مخطط SQLite');
                                    resolve();
                                }
                            }
                        });
                    });
                });
            });
        }
    }
};
